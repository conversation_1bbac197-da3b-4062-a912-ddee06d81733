# 同程酒店价格同步系统 - 多进程多标签页版本使用指南

## 概述

多进程多标签页版本是同程酒店价格同步系统的高性能版本，通过以下技术实现大幅性能提升：

- **多进程并行**: 不同酒店在不同进程中并行处理
- **多标签页并行**: 每个进程内的房型在不同标签页中并行处理
- **智能资源管理**: 自动管理进程和标签页资源
- **错误隔离**: 单个酒店或房型的错误不影响其他处理

## 性能对比

假设处理6个酒店，每个酒店4个房型，每个房型需要30秒：

| 模式 | 进程数 | 标签页数 | 理论并行度 | 预计时间 | 加速比 |
|------|--------|----------|------------|----------|--------|
| 原版单线程 | 1 | 1 | 1x | 12分钟 | 1.0x |
| 安全模式 | 2 | 2 | 4x | 3分钟 | 4.0x |
| 默认模式 | 2 | 3 | 6x | 2分钟 | 6.0x |
| 快速模式 | 3 | 5 | 15x | 48秒 | 15.0x |

## 快速开始

### 1. 基本使用

```bash
# 启动多进程版本
python tongcheng/main_multi_process.py

# 或使用快速启动脚本
python tongcheng/start_multi_process.py
```

### 2. 选择处理模式

启动后会提示选择处理模式：

```
📋 请选择处理模式:
1. 单进程模式 (兼容模式，适合调试)
2. 安全模式 (2进程×2标签页，稳定可靠)
3. 默认模式 (2进程×3标签页，平衡性能)
4. 快速模式 (3进程×5标签页，最高效率)
5. 自定义模式
6. 查看模式对比
```

**推荐选择**：
- 首次使用：选择 **安全模式**
- 日常使用：选择 **默认模式**
- 追求速度：选择 **快速模式**
- 调试问题：选择 **单进程模式**

### 3. 选择执行方式

```
📋 请选择执行方式:
1. 单次执行
2. 连续执行 (每2小时自动执行)
3. 系统健康检查
4. 退出
```

## 详细配置

### 预定义配置模式

#### 1. 安全模式 (推荐新手)
```python
SAFE_MULTI_PROCESS_CONFIG = MultiProcessConfig(
    max_processes=2,           # 2个进程
    multi_tab_config=SAFE_MODE_CONFIG  # 每进程2个标签页
)
```
- **特点**: 稳定可靠，资源占用适中
- **适用**: 生产环境，对稳定性要求高
- **理论加速**: 4倍

#### 2. 默认模式 (推荐日常)
```python
DEFAULT_MULTI_PROCESS_CONFIG = MultiProcessConfig(
    max_processes=2,           # 2个进程
    multi_tab_config=DEFAULT_CONFIG    # 每进程3个标签页
)
```
- **特点**: 平衡性能和稳定性
- **适用**: 大多数使用场景
- **理论加速**: 6倍

#### 3. 快速模式 (追求性能)
```python
FAST_MULTI_PROCESS_CONFIG = MultiProcessConfig(
    max_processes=3,           # 3个进程
    multi_tab_config=FAST_MODE_CONFIG  # 每进程5个标签页
)
```
- **特点**: 最高性能，但可能不够稳定
- **适用**: 对速度要求极高的场景
- **理论加速**: 15倍

#### 4. 单进程模式 (兼容调试)
```python
SINGLE_PROCESS_CONFIG = MultiProcessConfig(
    max_processes=1,           # 1个进程
    enable_multi_process=False,
    multi_tab_config=SINGLE_TAB_CONFIG # 1个标签页
)
```
- **特点**: 完全兼容原版，便于调试
- **适用**: 问题排查，兼容性测试
- **理论加速**: 1倍（无加速）

### 自定义配置

选择"自定义模式"可以手动配置：

```
🔧 自定义配置模式
请输入进程数量 (1-5, 推荐2-3): 2
请输入每进程标签页数量 (1-8, 推荐2-5): 3
是否使用无头模式? (y/N): n
```

## 系统要求

### 硬件要求

| 模式 | 最低内存 | 推荐内存 | CPU核心 | 网络 |
|------|----------|----------|---------|------|
| 单进程模式 | 2GB | 4GB | 2核 | 稳定 |
| 安全模式 | 4GB | 8GB | 4核 | 稳定 |
| 默认模式 | 6GB | 12GB | 4核 | 稳定 |
| 快速模式 | 8GB | 16GB | 6核+ | 高速稳定 |

### 软件要求

- Python 3.8+
- Chrome/Chromium 浏览器
- 所有原版系统依赖

## 监控和日志

### 日志文件

- **主日志**: `logs/workflow.log`
- **多进程日志**: `price_change_multi_process.log`
- **统计文件**: `price_change_count_multi_process.txt`

### 实时监控

系统会实时显示：
- 进程启动状态
- 酒店处理进度
- 成功/失败统计
- 性能指标

### 健康检查

```bash
# 执行系统健康检查
python tongcheng/main_multi_process.py
# 选择 "3. 系统健康检查"
```

## 故障排除

### 常见问题

#### 1. 进程启动失败
**现象**: 进程无法启动或立即退出
**解决**:
```bash
# 检查系统资源
# 降低进程数量或标签页数量
# 使用单进程模式调试
```

#### 2. 内存不足
**现象**: 系统变慢或进程被杀死
**解决**:
- 减少进程数量
- 减少标签页数量
- 启用无头模式
- 增加系统内存

#### 3. 浏览器崩溃
**现象**: 标签页无响应或浏览器崩溃
**解决**:
- 减少标签页数量
- 启用无头模式
- 更新Chrome浏览器
- 检查系统稳定性

#### 4. 网络超时
**现象**: 频繁的网络连接失败
**解决**:
- 增加超时时间
- 检查网络稳定性
- 减少并发数量

### 调试模式

遇到问题时，建议使用单进程模式进行调试：

```python
# 使用单进程单标签页模式
config = MultiProcessConfig.create_single_process_mode()
orchestrator = MultiProcessOrchestrator(config)
result = orchestrator.main_workflow()
```

### 性能调优

#### 1. 根据机器性能调整

```python
# 高性能机器 (16GB+ 内存, 8核+ CPU)
config = MultiProcessConfig.create_fast_mode()

# 中等性能机器 (8GB 内存, 4核 CPU)
config = MultiProcessConfig.create_default()

# 低性能机器 (4GB 内存, 2核 CPU)
config = MultiProcessConfig.create_safe_mode()
```

#### 2. 自定义超时时间

```python
config = MultiProcessConfig(
    max_processes=2,
    process_startup_timeout=90.0,      # 进程启动超时
    hotel_processing_timeout=1800.0,  # 酒店处理超时
    multi_tab_config=custom_tab_config
)
```

## 最佳实践

### 1. 生产环境部署

- 使用安全模式或默认模式
- 启用详细日志记录
- 设置资源监控
- 定期检查系统健康状态

### 2. 性能优化

- 根据网络状况调整并发数
- 监控系统资源使用情况
- 定期清理日志文件
- 优化浏览器配置

### 3. 错误处理

- 设置合理的重试次数
- 监控错误率
- 及时处理异常情况
- 保持系统更新

## 升级指南

### 从单进程版本升级

1. **备份现有配置和数据**
2. **测试多进程版本**
   ```bash
   # 先使用单进程模式测试
   python tongcheng/main_multi_process.py
   # 选择 "1. 单进程模式"
   ```
3. **逐步增加并发**
   - 先使用安全模式
   - 确认稳定后使用默认模式
   - 根据需要选择快速模式

### 配置迁移

原有的配置文件无需修改，多进程版本会自动使用现有配置。

## 技术架构

### 组件结构

```
MultiProcessOrchestrator (多进程协调器)
├── ProcessManager (进程管理器)
│   ├── ProcessMonitor (进程监控器)
│   └── ProcessPoolExecutor (进程池)
├── WorkflowCoordinator (工作流协调器)
├── ErrorHandler (错误处理器)
├── PerformanceMonitor (性能监控器)
└── MultiTabProcessor (多标签页处理器)
    ├── TabManager (标签页管理器)
    └── TongchengPusher (同程推送器)
```

### 处理流程

1. **数据获取阶段** (单进程)
   - 获取飞书酒店数据
   - 获取房型映射关系
   - 获取八大洲价格数据

2. **任务分配阶段**
   - 将酒店分配到不同进程
   - 每个进程内将房型分配到不同标签页

3. **并行处理阶段**
   - 多进程并行处理酒店
   - 每进程内多标签页并行处理房型

4. **结果汇总阶段**
   - 收集所有进程的处理结果
   - 生成统计报告和日志

## 支持和反馈

如果在使用过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 尝试使用单进程模式进行调试
3. 检查系统资源和网络状况
4. 参考故障排除章节

多进程多标签页版本为同程酒店价格同步系统提供了强大的性能提升，合理配置和使用可以大幅提高工作效率。

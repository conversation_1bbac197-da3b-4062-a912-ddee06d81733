#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试点击统计功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tongcheng.workflow.workflow import ClickStatistics
from datetime import datetime
import json

def test_click_statistics():
    """测试点击统计功能"""
    
    print("=== 测试点击统计功能 ===")
    
    # 创建统计对象
    stats = ClickStatistics()
    
    # 模拟添加统计数据
    print("\n1. 添加统计数据...")
    
    # 酒店1：新加坡费尔蒙酒店
    hotel1 = "新加坡费尔蒙酒店"
    stats.add_hotel_room_count(hotel1, 3)  # 3个房型
    stats.add_room_click(hotel1, "豪华大床房", 25)  # 25次点击
    stats.add_room_click(hotel1, "豪华大床房（港景，带阳台）", 32)  # 32次点击
    stats.add_room_click(hotel1, "特色套房", 18)  # 18次点击
    
    # 酒店2：香港半岛酒店
    hotel2 = "香港半岛酒店"
    stats.add_hotel_room_count(hotel2, 2)  # 2个房型
    stats.add_room_click(hotel2, "豪华客房", 28)  # 28次点击
    stats.add_room_click(hotel2, "半岛套房", 45)  # 45次点击
    
    # 酒店3：东京帝国酒店
    hotel3 = "东京帝国酒店"
    stats.add_hotel_room_count(hotel3, 4)  # 4个房型
    stats.add_room_click(hotel3, "帝国客房", 22)  # 22次点击
    stats.add_room_click(hotel3, "帝国套房", 38)  # 38次点击
    stats.add_room_click(hotel3, "皇家套房", 55)  # 55次点击
    stats.add_room_click(hotel3, "总统套房", 67)  # 67次点击
    
    print("✓ 统计数据添加完成")
    
    # 2. 打印统计信息
    print("\n2. 打印统计报告...")
    stats.print_statistics()
    
    # 3. 获取统计摘要
    print("\n3. 获取统计摘要...")
    summary = stats.get_statistics_summary()
    print("统计摘要:")
    print(f"  - 总点击次数: {summary['总点击次数']}")
    print(f"  - 处理酒店数量: {summary['处理酒店数量']}")
    print(f"  - 执行时间: {summary['执行时间']['总耗时']}")
    
    # 4. 保存到文件
    print("\n4. 保存统计信息到文件...")
    test_filename = "test_click_statistics.json"
    stats.save_to_file(test_filename)
    
    # 验证文件是否创建成功
    if os.path.exists(test_filename):
        print(f"✓ 统计文件创建成功: {test_filename}")
        
        # 读取并验证文件内容
        with open(test_filename, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        print("文件内容验证:")
        print(f"  - 总点击次数: {saved_data['总点击次数']}")
        print(f"  - 处理酒店数量: {saved_data['处理酒店数量']}")
        
        # 清理测试文件
        os.remove(test_filename)
        print(f"✓ 测试文件已清理: {test_filename}")
    else:
        print("✗ 统计文件创建失败")
        return False
    
    # 5. 验证统计数据的正确性
    print("\n5. 验证统计数据...")
    
    expected_total_clicks = 25 + 32 + 18 + 28 + 45 + 22 + 38 + 55 + 67  # 330
    actual_total_clicks = stats.total_clicks
    
    if actual_total_clicks == expected_total_clicks:
        print(f"✓ 总点击次数正确: {actual_total_clicks}")
    else:
        print(f"✗ 总点击次数错误: 期望 {expected_total_clicks}, 实际 {actual_total_clicks}")
        return False
    
    expected_hotel_count = 3
    actual_hotel_count = len(stats.hotel_clicks)
    
    if actual_hotel_count == expected_hotel_count:
        print(f"✓ 酒店数量正确: {actual_hotel_count}")
    else:
        print(f"✗ 酒店数量错误: 期望 {expected_hotel_count}, 实际 {actual_hotel_count}")
        return False
    
    # 验证各酒店点击次数
    expected_hotel_clicks = {
        hotel1: 75,  # 25 + 32 + 18
        hotel2: 73,  # 28 + 45
        hotel3: 182  # 22 + 38 + 55 + 67
    }
    
    for hotel, expected_clicks in expected_hotel_clicks.items():
        actual_clicks = stats.hotel_clicks.get(hotel, 0)
        if actual_clicks == expected_clicks:
            print(f"✓ {hotel} 点击次数正确: {actual_clicks}")
        else:
            print(f"✗ {hotel} 点击次数错误: 期望 {expected_clicks}, 实际 {actual_clicks}")
            return False
    
    print("\n=== 所有测试通过！点击统计功能正常工作 ===")
    return True

def test_incremental_statistics():
    """测试增量统计功能"""
    
    print("\n=== 测试增量统计功能 ===")
    
    stats = ClickStatistics()
    hotel_name = "测试酒店"
    room_name = "测试房型"
    
    # 多次添加同一房型的点击
    stats.add_room_click(hotel_name, room_name, 10)
    stats.add_room_click(hotel_name, room_name, 5)
    stats.add_room_click(hotel_name, room_name, 8)
    
    expected_total = 23  # 10 + 5 + 8
    actual_total = stats.room_clicks[hotel_name][room_name]
    
    if actual_total == expected_total:
        print(f"✓ 增量统计正确: {room_name} 总点击次数 {actual_total}")
    else:
        print(f"✗ 增量统计错误: 期望 {expected_total}, 实际 {actual_total}")
        return False
    
    # 验证酒店总点击次数
    hotel_total = stats.hotel_clicks[hotel_name]
    if hotel_total == expected_total:
        print(f"✓ 酒店总点击次数正确: {hotel_total}")
    else:
        print(f"✗ 酒店总点击次数错误: 期望 {expected_total}, 实际 {hotel_total}")
        return False
    
    print("✓ 增量统计功能正常")
    return True

if __name__ == "__main__":
    success1 = test_click_statistics()
    success2 = test_incremental_statistics()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！点击统计功能实现成功。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！需要检查点击统计功能。")
        sys.exit(1)

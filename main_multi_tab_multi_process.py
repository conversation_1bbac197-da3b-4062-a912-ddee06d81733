"""
同程酒店价格维护系统 - 多标签页多进程版本
终极版本：结合多进程和多标签页的优势，实现最高效的价格维护
"""

import sys
import time
import os
from typing import Dict, Any
from datetime import datetime

from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    DEFAULT_MULTI_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG,
    SINGLE_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import (
    MultiTabConfig,
    DEFAULT_CONFIG,
    FAST_MODE_CONFIG,
    SAFE_MODE_CONFIG,
    SINGLE_TAB_CONFIG,
    MULTI_PROCESS_TAB_CONFIG
)
from tongcheng.tools.logger import get_workflow_logger

logger = get_workflow_logger()


def print_banner():
    """打印系统横幅"""
    print("=" * 90)
    print("🚀 同程酒店价格维护系统 - 多标签页多进程终极版本")
    print("=" * 90)
    print("✨ 终极特性:")
    print("   • 多进程 × 多标签页 = 超强并行处理能力")
    print("   • 理论加速比: 进程数 × 标签页数")
    print("   • 智能资源管理和负载均衡")
    print("   • 完善的错误恢复和重试机制")
    print("   • 实时性能监控和详细日志")
    print("   • 灵活的配置选项和模式切换")
    print("=" * 90)
    print()


def show_config_modes():
    """显示配置模式选项"""
    print("📋 可用配置模式:")
    print()
    print("1. 🚀 极速模式 (Fast Mode)")
    print("   • 3进程 × 5标签页 = 15倍并行度")
    print("   • 适合: 高性能机器，大量酒店处理")
    print("   • 风险: 资源消耗大，可能不稳定")
    print()
    print("2. ⚡ 高效模式 (Default Mode)")
    print("   • 2进程 × 3标签页 = 6倍并行度")
    print("   • 适合: 中等性能机器，日常使用")
    print("   • 平衡: 效率与稳定性的最佳平衡")
    print()
    print("3. 🛡️ 安全模式 (Safe Mode)")
    print("   • 2进程 × 2标签页 = 4倍并行度")
    print("   • 适合: 低配置机器，稳定性优先")
    print("   • 特点: 更长超时，更多重试")
    print()
    print("4. 🔧 自定义模式 (Custom Mode)")
    print("   • 自定义进程数和标签页数")
    print("   • 适合: 有特殊需求的用户")
    print("   • 灵活: 完全可控的配置")
    print()
    print("5. 🐌 兼容模式 (Single Mode)")
    print("   • 1进程 × 1标签页 = 传统模式")
    print("   • 适合: 调试或兼容性测试")
    print("   • 稳定: 最稳定但最慢")
    print()


def select_config_mode() -> MultiProcessConfig:
    """选择配置模式"""
    show_config_modes()
    
    while True:
        try:
            choice = input("请选择配置模式 (1-5): ").strip()
            
            if choice == "1":
                print("✅ 已选择极速模式")
                return FAST_MULTI_PROCESS_CONFIG
            elif choice == "2":
                print("✅ 已选择高效模式")
                return DEFAULT_MULTI_PROCESS_CONFIG
            elif choice == "3":
                print("✅ 已选择安全模式")
                return SAFE_MULTI_PROCESS_CONFIG
            elif choice == "4":
                print("✅ 已选择自定义模式")
                return create_custom_config()
            elif choice == "5":
                print("✅ 已选择兼容模式")
                return SINGLE_PROCESS_CONFIG
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 输入错误: {e}")


def create_custom_config() -> MultiProcessConfig:
    """创建自定义配置"""
    print("\n🔧 自定义配置向导:")
    print("=" * 50)
    
    try:
        # 进程数配置
        print("\n📊 进程配置:")
        max_processes = int(input("进程数量 (1-6, 推荐2-3): "))
        if max_processes < 1 or max_processes > 6:
            print("⚠️ 进程数量超出推荐范围，使用默认值2")
            max_processes = 2
        
        # 标签页配置
        print("\n🗂️ 标签页配置:")
        max_tabs = int(input("每进程标签页数量 (1-8, 推荐2-4): "))
        if max_tabs < 1 or max_tabs > 8:
            print("⚠️ 标签页数量超出推荐范围，使用默认值3")
            max_tabs = 3
        
        # 性能配置
        print("\n⚡ 性能配置:")
        room_interval = float(input("房型处理间隔/秒 (0.5-3.0, 推荐1.0-2.0): "))
        if room_interval < 0.5 or room_interval > 3.0:
            print("⚠️ 间隔时间超出推荐范围，使用默认值1.5")
            room_interval = 1.5
        
        hotel_interval = float(input("酒店处理间隔/秒 (1.0-5.0, 推荐2.0): "))
        if hotel_interval < 1.0 or hotel_interval > 5.0:
            print("⚠️ 间隔时间超出推荐范围，使用默认值2.0")
            hotel_interval = 2.0
        
        # 浏览器配置
        print("\n🌐 浏览器配置:")
        headless = input("是否使用无头模式? (y/N): ").strip().lower() == 'y'
        
        # 创建多标签页配置
        multi_tab_config = MultiTabConfig(
            max_tabs=max_tabs,
            enable_multi_tab=max_tabs > 1,
            room_interval=room_interval,
            headless=headless,
            enable_detailed_logging=True
        )
        
        # 创建多进程配置
        config = MultiProcessConfig(
            max_processes=max_processes,
            enable_multi_process=max_processes > 1,
            multi_tab_config=multi_tab_config,
            hotel_interval=hotel_interval
        )
        
        # 显示配置摘要
        print("\n📋 配置摘要:")
        print(f"   进程数量: {config.max_processes}")
        print(f"   每进程标签页: {config.multi_tab_config.max_tabs}")
        print(f"   理论并行度: {config.max_processes * config.multi_tab_config.max_tabs}x")
        print(f"   房型间隔: {config.multi_tab_config.room_interval}秒")
        print(f"   酒店间隔: {config.hotel_interval}秒")
        print(f"   无头模式: {config.multi_tab_config.headless}")
        
        confirm = input("\n确认使用此配置? (Y/n): ").strip().lower()
        if confirm in ['', 'y', 'yes']:
            return config
        else:
            print("重新配置...")
            return create_custom_config()
        
    except ValueError:
        print("⚠️ 输入格式错误，使用默认配置")
        return DEFAULT_MULTI_PROCESS_CONFIG
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(0)


def show_config_details(config: MultiProcessConfig):
    """显示详细配置信息"""
    print("\n" + "=" * 60)
    print("📊 当前配置详情")
    print("=" * 60)
    
    # 基本信息
    print(f"🔧 基本配置:")
    print(f"   多进程模式: {'✅ 启用' if config.enable_multi_process else '❌ 禁用'}")
    print(f"   进程数量: {config.max_processes}")
    print(f"   多标签页模式: {'✅ 启用' if config.multi_tab_config.enable_multi_tab else '❌ 禁用'}")
    print(f"   每进程标签页: {config.multi_tab_config.max_tabs}")
    
    # 性能信息
    total_parallel = config.max_processes * config.multi_tab_config.max_tabs
    print(f"\n⚡ 性能配置:")
    print(f"   理论并行度: {total_parallel}x")
    print(f"   房型处理间隔: {config.multi_tab_config.room_interval}秒")
    print(f"   酒店处理间隔: {config.hotel_interval}秒")
    print(f"   进程启动间隔: {config.process_start_delay}秒")
    
    # 超时配置
    print(f"\n⏱️ 超时配置:")
    print(f"   单个酒店处理超时: {config.hotel_processing_timeout}秒")
    print(f"   单个房型处理超时: {config.multi_tab_config.room_processing_timeout}秒")
    print(f"   标签页获取超时: {config.multi_tab_config.tab_acquire_timeout}秒")
    
    # 浏览器配置
    print(f"\n🌐 浏览器配置:")
    print(f"   无头模式: {'✅ 启用' if config.multi_tab_config.headless else '❌ 禁用'}")
    print(f"   Cookie路径: {config.multi_tab_config.cookies_path}")
    
    # 日志配置
    print(f"\n📝 日志配置:")
    print(f"   详细日志: {'✅ 启用' if config.multi_tab_config.enable_detailed_logging else '❌ 禁用'}")
    print(f"   性能监控: {'✅ 启用' if config.log_process_metrics else '❌ 禁用'}")
    
    print("=" * 60)


def check_system_requirements(config: MultiProcessConfig) -> bool:
    """检查系统要求"""
    print("\n🔍 系统要求检查:")
    
    # 检查Cookie文件
    cookies_path = config.multi_tab_config.cookies_path
    if not os.path.exists(cookies_path):
        print(f"❌ Cookie文件不存在: {cookies_path}")
        print("   请确保已正确配置Cookie文件")
        return False
    else:
        print(f"✅ Cookie文件存在: {cookies_path}")
    
    # 检查并行度
    total_parallel = config.max_processes * config.multi_tab_config.max_tabs
    if total_parallel > 20:
        print(f"⚠️ 并行度过高: {total_parallel}x")
        print("   建议降低进程数或标签页数以避免系统过载")
        confirm = input("   是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            return False
    else:
        print(f"✅ 并行度合理: {total_parallel}x")
    
    # 检查内存要求（估算）
    estimated_memory = config.max_processes * config.multi_tab_config.max_tabs * 200  # 每个标签页约200MB
    if estimated_memory > 4000:  # 超过4GB
        print(f"⚠️ 预估内存需求: {estimated_memory}MB")
        print("   请确保系统有足够内存")
    else:
        print(f"✅ 预估内存需求: {estimated_memory}MB")
    
    print("✅ 系统要求检查通过")
    return True


def run_workflow(config: MultiProcessConfig) -> Dict[str, Any]:
    """运行工作流程"""
    print("\n" + "🚀" * 30)
    print("开始执行多标签页多进程工作流程")
    print("🚀" * 30)
    
    start_time = time.time()
    
    try:
        # 创建多进程协调器
        orchestrator = MultiProcessOrchestrator(config)
        
        # 执行健康检查
        health_status = orchestrator.health_check()
        if not health_status.get("healthy", False):
            print("❌ 系统健康检查失败")
            print(f"   错误: {health_status.get('error', '未知错误')}")
            return {"status": "error", "message": "系统健康检查失败"}
        
        print("✅ 系统健康检查通过")
        
        # 执行主工作流程
        result = orchestrator.main_workflow()
        
        # 计算总耗时
        total_time = time.time() - start_time
        result["total_execution_time"] = total_time
        
        return result
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return {"status": "interrupted", "message": "用户中断操作"}
    except Exception as e:
        print(f"\n❌ 工作流程执行失败: {str(e)}")
        logger.error(f"工作流程执行失败: {str(e)}")
        return {"status": "error", "message": str(e)}


def show_execution_summary(result: Dict[str, Any]):
    """显示执行摘要"""
    print("\n" + "📊" * 30)
    print("执行结果摘要")
    print("📊" * 30)
    
    status = result.get("status", "unknown")
    if status == "success":
        print("🎉 执行状态: 成功")
    elif status == "error":
        print("❌ 执行状态: 失败")
    elif status == "interrupted":
        print("⚠️ 执行状态: 中断")
    else:
        print(f"❓ 执行状态: {status}")
    
    # 显示处理统计
    summary = result.get("summary", {})
    if summary:
        print(f"\n📈 处理统计:")
        print(f"   总酒店数: {summary.get('total_hotels', 0)}")
        print(f"   成功酒店数: {summary.get('success_hotels', 0)}")
        print(f"   失败酒店数: {summary.get('failed_hotels', 0)}")
        print(f"   成功率: {summary.get('success_rate', 0):.1f}%")
        print(f"   总处理时间: {summary.get('total_processing_time', 0):.2f}秒")
        print(f"   平均处理时间: {summary.get('average_processing_time', 0):.2f}秒/酒店")
        print(f"   总房型数: {summary.get('total_rooms_processed', 0)}")
    
    # 显示总耗时
    total_time = result.get("total_execution_time", 0)
    if total_time > 0:
        print(f"\n⏱️ 总执行时间: {total_time:.2f}秒")
        
        # 计算效率提升
        if summary.get('total_hotels', 0) > 0:
            time_per_hotel = total_time / summary['total_hotels']
            print(f"   平均每酒店: {time_per_hotel:.2f}秒")
    
    # 显示错误信息
    if status == "error":
        message = result.get("message", "未知错误")
        print(f"\n❌ 错误信息: {message}")
    
    # 显示性能报告
    execution_report = result.get("execution_report", {})
    if execution_report:
        performance_metrics = execution_report.get("performance_metrics", {})
        if performance_metrics:
            print(f"\n📊 性能指标:")
            for key, value in performance_metrics.items():
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value}")
    
    print("📊" * 30)


def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 选择配置模式
        config = select_config_mode()
        
        # 显示配置详情
        show_config_details(config)
        
        # 确认执行
        print("\n" + "⚡" * 50)
        confirm = input("确认开始执行? (Y/n): ").strip().lower()
        if confirm not in ['', 'y', 'yes']:
            print("👋 操作已取消")
            return
        
        # 检查系统要求
        if not check_system_requirements(config):
            print("❌ 系统要求检查失败，操作已取消")
            return
        
        # 运行工作流程
        result = run_workflow(config)
        
        # 显示执行摘要
        show_execution_summary(result)
        
        # 保存结果（可选）
        save_result = input("\n是否保存执行结果到文件? (y/N): ").strip().lower()
        if save_result == 'y':
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"execution_result_{timestamp}.json"
            
            import json
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 执行结果已保存到: {result_file}")
        
        print("\n🎉 程序执行完成！")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        logger.error(f"程序执行异常: {str(e)}")
    finally:
        print("\n感谢使用同程酒店价格维护系统！")


if __name__ == "__main__":
    main()

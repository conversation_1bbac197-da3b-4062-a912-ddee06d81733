"""
多标签页多进程功能演示脚本
展示系统的核心功能和配置选项
"""

import sys
import time
import os
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    DEFAULT_MULTI_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG,
    SINGLE_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import (
    MultiTabConfig,
    DEFAULT_CONFIG,
    FAST_MODE_CONFIG,
    SAFE_MODE_CONFIG,
    SINGLE_TAB_CONFIG,
    MULTI_PROCESS_TAB_CONFIG
)
from tongcheng.workflow.multi_tab_performance_monitor import MultiTabPerformanceMonitor


def print_banner():
    """打印演示横幅"""
    print("🎭" * 40)
    print("同程酒店价格维护系统")
    print("多标签页多进程功能演示")
    print("🎭" * 40)
    print()


def demo_config_options():
    """演示配置选项"""
    print("📋 配置选项演示")
    print("=" * 60)
    
    configs = [
        ("🐌 兼容模式", SINGLE_PROCESS_CONFIG),
        ("🛡️ 安全模式", SAFE_MULTI_PROCESS_CONFIG),
        ("⚡ 标准模式", DEFAULT_MULTI_PROCESS_CONFIG),
        ("🚀 高速模式", FAST_MULTI_PROCESS_CONFIG),
    ]
    
    for name, config in configs:
        parallel = config.max_processes * config.multi_tab_config.max_tabs
        print(f"\n{name}:")
        print(f"   进程数: {config.max_processes}")
        print(f"   标签页数: {config.multi_tab_config.max_tabs}")
        print(f"   并行度: {parallel}x")
        print(f"   酒店间隔: {config.hotel_interval}s")
        print(f"   房型间隔: {config.multi_tab_config.room_interval}s")
        print(f"   无头模式: {config.multi_tab_config.headless}")
    
    print("\n" + "=" * 60)


def demo_custom_config():
    """演示自定义配置创建"""
    print("\n🔧 自定义配置演示")
    print("=" * 60)
    
    # 创建自定义多标签页配置
    custom_tab_config = MultiTabConfig(
        max_tabs=4,
        enable_multi_tab=True,
        room_interval=1.2,
        tab_switch_delay=2.0,
        headless=False,
        enable_detailed_logging=True
    )
    
    print("自定义多标签页配置:")
    print(f"   最大标签页: {custom_tab_config.max_tabs}")
    print(f"   房型间隔: {custom_tab_config.room_interval}s")
    print(f"   标签页切换延迟: {custom_tab_config.tab_switch_delay}s")
    print(f"   详细日志: {custom_tab_config.enable_detailed_logging}")
    
    # 创建自定义多进程配置
    custom_process_config = MultiProcessConfig(
        max_processes=3,
        enable_multi_process=True,
        multi_tab_config=custom_tab_config,
        hotel_interval=1.8,
        hotel_processing_timeout=1500.0
    )
    
    parallel = custom_process_config.max_processes * custom_process_config.multi_tab_config.max_tabs
    
    print(f"\n自定义多进程配置:")
    print(f"   进程数: {custom_process_config.max_processes}")
    print(f"   酒店间隔: {custom_process_config.hotel_interval}s")
    print(f"   处理超时: {custom_process_config.hotel_processing_timeout}s")
    print(f"   总并行度: {parallel}x")
    
    print("\n" + "=" * 60)


def demo_performance_monitor():
    """演示性能监控功能"""
    print("\n📊 性能监控演示")
    print("=" * 60)
    
    # 创建性能监控器
    monitor = MultiTabPerformanceMonitor()
    
    print("启动性能监控...")
    monitor.start_monitoring(total_processes=2, tabs_per_process=3)
    
    # 模拟进程活动
    print("模拟进程活动...")
    monitor.record_process_start(process_id=1001, tab_count=3)
    monitor.record_process_start(process_id=1002, tab_count=3)
    
    # 模拟处理活动
    print("模拟酒店处理...")
    hotels = [
        ("酒店A", True, 5.2, 3),
        ("酒店B", True, 4.8, 2),
        ("酒店C", False, 2.1, 1),
        ("酒店D", True, 6.3, 4),
        ("酒店E", True, 3.9, 2),
    ]
    
    for i, (hotel_name, success, time_taken, room_count) in enumerate(hotels):
        process_id = 1001 if i % 2 == 0 else 1002
        monitor.record_hotel_processed(process_id, success, time_taken, room_count)
        print(f"   {hotel_name}: {'✅' if success else '❌'} ({time_taken}s, {room_count}房型)")
    
    # 模拟标签页活动
    print("模拟标签页活动...")
    tab_activities = [
        (1001, 0, True, 1.5),
        (1001, 1, True, 1.8),
        (1001, 2, False, 2.2),
        (1002, 0, True, 1.3),
        (1002, 1, True, 1.6),
        (1002, 2, True, 1.4),
    ]
    
    for process_id, tab_id, success, time_taken in tab_activities:
        monitor.record_tab_activity(process_id, tab_id, success, time_taken)
    
    # 模拟错误
    monitor.record_error(1001, "连接超时", "无法连接到目标服务器")
    monitor.record_error(1002, "页面加载失败", "页面元素未找到")
    
    # 等待一下让监控收集数据
    time.sleep(2)
    
    # 获取实时统计
    real_time_stats = monitor.get_real_time_stats()
    print(f"\n实时统计:")
    print(f"   运行时长: {real_time_stats['system_metrics']['duration']:.1f}s")
    print(f"   处理酒店: {real_time_stats['system_metrics']['total_hotels']}")
    print(f"   处理房型: {real_time_stats['system_metrics']['total_rooms']}")
    print(f"   成功率: {real_time_stats['system_metrics']['success_rate']:.1f}%")
    print(f"   吞吐量: {real_time_stats['system_metrics']['actual_throughput']:.2f} 酒店/秒")
    
    # 生成详细报告
    report = monitor.generate_performance_report()
    
    print(f"\n详细性能报告:")
    summary = report['summary']
    print(f"   总并行度: {summary['theoretical_parallel']}x")
    print(f"   成功酒店: {summary['successful_hotels']}/{summary['total_hotels']}")
    print(f"   整体成功率: {summary['overall_success_rate']:.1f}%")
    print(f"   实际吞吐量: {summary['actual_throughput']:.2f} 酒店/秒")
    
    if 'process_analysis' in report:
        process_analysis = report['process_analysis']
        print(f"   平均吞吐量: {process_analysis['avg_throughput']:.2f} 酒店/秒")
        print(f"   最大吞吐量: {process_analysis['max_throughput']:.2f} 酒店/秒")
    
    if 'efficiency_analysis' in report:
        efficiency = report['efficiency_analysis']
        print(f"   并行效率: {efficiency['parallel_efficiency']:.1f}%")
        print(f"   加速因子: {efficiency['speedup_factor']:.1f}x")
    
    # 停止监控
    monitor.stop_monitoring()
    print("\n性能监控已停止")
    
    print("\n" + "=" * 60)


def demo_config_validation():
    """演示配置验证功能"""
    print("\n✅ 配置验证演示")
    print("=" * 60)
    
    # 测试有效配置
    valid_configs = [
        ("最小配置", MultiTabConfig(max_tabs=1, enable_multi_tab=False)),
        ("标准配置", MultiTabConfig(max_tabs=3, enable_multi_tab=True)),
        ("高性能配置", MultiTabConfig(max_tabs=6, enable_multi_tab=True)),
    ]
    
    print("有效配置测试:")
    for name, config in valid_configs:
        valid = config.validate()
        status = "✅ 通过" if valid else "❌ 失败"
        print(f"   {status} {name}")
    
    # 测试无效配置
    invalid_configs = [
        ("零标签页", MultiTabConfig(max_tabs=0)),
        ("负数标签页", MultiTabConfig(max_tabs=-1)),
        ("负超时时间", MultiTabConfig(tab_acquire_timeout=-1)),
    ]
    
    print("\n无效配置测试:")
    for name, config in invalid_configs:
        valid = config.validate()
        status = "✅ 正确拒绝" if not valid else "❌ 错误通过"
        print(f"   {status} {name}")
    
    print("\n" + "=" * 60)


def demo_performance_comparison():
    """演示性能对比"""
    print("\n⚡ 性能对比演示")
    print("=" * 60)
    
    # 模拟不同配置的性能数据
    performance_data = [
        {
            "name": "单线程模式",
            "config": "1进程 × 1标签页",
            "parallel": 1,
            "time": 120.0,
            "speedup": 1.0,
            "efficiency": 100.0
        },
        {
            "name": "安全模式",
            "config": "2进程 × 2标签页",
            "parallel": 4,
            "time": 32.0,
            "speedup": 3.75,
            "efficiency": 93.8
        },
        {
            "name": "标准模式",
            "config": "2进程 × 3标签页",
            "parallel": 6,
            "time": 22.0,
            "speedup": 5.45,
            "efficiency": 90.9
        },
        {
            "name": "高速模式",
            "config": "3进程 × 5标签页",
            "parallel": 15,
            "time": 10.0,
            "speedup": 12.0,
            "efficiency": 80.0
        }
    ]
    
    print("性能对比 (处理50个酒店):")
    print()
    print(f"{'模式':<12} {'配置':<15} {'并行度':<8} {'耗时':<10} {'加速比':<10} {'效率':<8}")
    print("-" * 70)
    
    for data in performance_data:
        print(f"{data['name']:<12} {data['config']:<15} {data['parallel']:<8} "
              f"{data['time']:.1f}分钟{'':<3} {data['speedup']:.1f}x{'':<5} {data['efficiency']:.1f}%")
    
    print("\n关键观察:")
    print("• 并行度越高，处理时间越短")
    print("• 但效率会随着并行度增加而略有下降")
    print("• 标准模式提供了最佳的效率与速度平衡")
    print("• 高速模式适合大量数据处理")
    
    print("\n" + "=" * 60)


def demo_usage_scenarios():
    """演示使用场景"""
    print("\n🎯 使用场景演示")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "🏢 小型酒店管理公司",
            "description": "管理10-20个酒店，每日价格更新",
            "recommended": "安全模式",
            "config": "2进程 × 2标签页",
            "reason": "稳定可靠，适合小规模操作"
        },
        {
            "name": "🏨 中型连锁酒店",
            "description": "管理50-100个酒店，频繁价格调整",
            "recommended": "标准模式",
            "config": "2进程 × 3标签页",
            "reason": "效率与稳定性平衡，适合日常使用"
        },
        {
            "name": "🏙️ 大型酒店集团",
            "description": "管理200+酒店，批量价格维护",
            "recommended": "高速模式",
            "config": "3进程 × 5标签页",
            "reason": "最高效率，适合大规模批量处理"
        },
        {
            "name": "🔧 系统测试/调试",
            "description": "开发测试，功能验证",
            "recommended": "兼容模式",
            "config": "1进程 × 1标签页",
            "reason": "单线程执行，便于调试和问题定位"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        print(f"   场景: {scenario['description']}")
        print(f"   推荐: {scenario['recommended']} ({scenario['config']})")
        print(f"   原因: {scenario['reason']}")
    
    print("\n" + "=" * 60)


def main():
    """主演示函数"""
    try:
        print_banner()
        
        print("🎬 开始功能演示...")
        print()
        
        # 演示各个功能
        demo_config_options()
        demo_custom_config()
        demo_performance_monitor()
        demo_config_validation()
        demo_performance_comparison()
        demo_usage_scenarios()
        
        print("\n🎉 演示完成!")
        print()
        print("📚 接下来您可以:")
        print("   1. 运行测试: python test_multi_tab_multi_process.py")
        print("   2. 快速启动: python quick_start_multi_tab_multi_process.py")
        print("   3. 完整功能: python main_multi_tab_multi_process.py")
        print("   4. 查看文档: README_MULTI_TAB_MULTI_PROCESS.md")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示执行异常: {str(e)}")


if __name__ == "__main__":
    main()

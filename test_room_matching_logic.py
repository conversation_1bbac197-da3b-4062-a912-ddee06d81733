#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试房型匹配逻辑的修改
验证新的匹配逻辑：只有同程房型在映射关系中存在但八大洲没有对应房型时才记录错误
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tongcheng.workflow.data_processor import DataProcessor
from tongcheng.workflow.error_handler import ErrorHandler
from tongcheng.tools.logger import setup_logging

def test_room_matching_logic():
    """测试房型匹配逻辑"""
    
    # 设置日志
    setup_logging(log_level="INFO")
    
    # 创建错误处理器
    error_handler = ErrorHandler()
    
    # 创建数据处理器
    data_processor = DataProcessor(error_handler=error_handler)
    
    # 模拟八大洲房型数据（从API获取的实际房型）
    hotel_rooms = [
        {
            "roomName": "豪华房（南楼）(大床)",
            "roomCode": "198831",
            "averagePriceList": [{"2025-07-08": "3021.0"}]
        },
        {
            "roomName": "豪华港景客房（南楼）(大床)",
            "roomCode": "198841",
            "averagePriceList": [{"2025-07-08": "3342.0"}]
        },
        {
            "roomName": "特色套房（北楼）(大床)",
            "roomCode": "198821",
            "averagePriceList": [{"2025-07-08": "3226.0"}]
        }
    ]
    
    # 模拟飞书房型映射关系
    room_mappings = [
        {
            "同程酒店名称": "新加坡费尔蒙酒店",
            "八大洲酒店名称": "新加坡费尔蒙酒店",
            "同程房型名称": "豪华大床房（港景，带阳台）",
            "八大洲房型名称": "豪华港景客房（南楼）",
            "床型": "大床"
        },
        {
            "同程酒店名称": "新加坡费尔蒙酒店",
            "八大洲酒店名称": "新加坡费尔蒙酒店",
            "同程房型名称": "豪华大床房",
            "八大洲房型名称": "豪华房（南楼）",
            "床型": "大床"
        },
        {
            "同程酒店名称": "新加坡费尔蒙酒店",
            "八大洲酒店名称": "新加坡费尔蒙酒店",
            "同程房型名称": "大使套房（南楼+北楼）",
            "八大洲房型名称": "大使套房（南楼+北楼）",
            "床型": "双床"
        },
        {
            "同程酒店名称": "新加坡费尔蒙酒店",
            "八大洲酒店名称": "新加坡费尔蒙酒店",
            "同程房型名称": "费尔蒙金尊客房",
            "八大洲房型名称": "费尔蒙金尊客房（南楼）",
            "床型": "大床"
        }
    ]
    
    print("=" * 60)
    print("测试房型匹配逻辑")
    print("=" * 60)
    
    print("\n八大洲实际房型:")
    for room in hotel_rooms:
        print(f"  - {room['roomName']} (编码: {room['roomCode']})")
    
    print("\n飞书房型映射关系:")
    for mapping in room_mappings:
        tongcheng_name = mapping['同程房型名称']
        badazhou_name = mapping['八大洲房型名称']
        bed_type = mapping['床型']
        full_name = f"{badazhou_name}({bed_type})" if bed_type else badazhou_name
        print(f"  - 同程: {tongcheng_name} -> 八大洲: {full_name}")
    
    print("\n" + "=" * 60)
    print("开始房型匹配...")
    print("=" * 60)
    
    # 清空之前的错误记录
    error_handler.clear_errors()
    
    # 执行房型匹配
    matched_rooms = data_processor.match_rooms(hotel_rooms, room_mappings)
    
    print(f"\n匹配结果:")
    print(f"成功匹配的房型数量: {len(matched_rooms)}")
    
    for room in matched_rooms:
        print(f"  ✓ {room['八大洲房型名称']} -> {room['同程房型名称']}")
    
    # 获取错误记录
    errors = error_handler.get_errors()
    
    print(f"\n错误记录:")
    print(f"错误数量: {len(errors)}")
    
    if errors:
        for error in errors:
            print(f"  ✗ {error}")
    else:
        print("  无错误记录")
    
    print("\n" + "=" * 60)
    print("测试结果分析:")
    print("=" * 60)
    
    # 分析结果
    print("\n预期行为:")
    print("1. 八大洲有房型但同程没有对应的情况 -> 不记录为错误，只记录信息")
    print("2. 同程房型在映射关系中存在但八大洲没有对应房型 -> 记录为错误")
    
    print("\n实际结果:")
    
    # 检查八大洲独有房型（应该不记录为错误）
    badazhou_only_rooms = []
    for room in hotel_rooms:
        room_name = room['roomName']
        found_mapping = False
        for mapping in room_mappings:
            badazhou_name = mapping['八大洲房型名称']
            bed_type = mapping['床型']
            full_name = f"{badazhou_name}({bed_type})" if bed_type else badazhou_name
            if full_name == room_name or badazhou_name == room_name:
                found_mapping = True
                break
        if not found_mapping:
            badazhou_only_rooms.append(room_name)
    
    if badazhou_only_rooms:
        print(f"八大洲独有房型 ({len(badazhou_only_rooms)} 个):")
        for room_name in badazhou_only_rooms:
            print(f"  - {room_name} (应该不记录为错误)")
    
    # 检查同程独有房型（应该记录为错误）
    tongcheng_only_mappings = []
    badazhou_room_names = {room['roomName'] for room in hotel_rooms}
    
    for mapping in room_mappings:
        badazhou_name = mapping['八大洲房型名称']
        bed_type = mapping['床型']
        full_name = f"{badazhou_name}({bed_type})" if bed_type else badazhou_name
        
        if full_name not in badazhou_room_names and badazhou_name not in badazhou_room_names:
            tongcheng_only_mappings.append({
                'tongcheng_name': mapping['同程房型名称'],
                'full_name': full_name
            })
    
    if tongcheng_only_mappings:
        print(f"\n同程独有房型映射 ({len(tongcheng_only_mappings)} 个):")
        for mapping in tongcheng_only_mappings:
            print(f"  - {mapping['tongcheng_name']} -> {mapping['full_name']} (应该记录为错误)")
    
    print(f"\n实际错误记录数量: {len(errors)}")
    print(f"预期错误记录数量: {len(tongcheng_only_mappings)}")
    
    if len(errors) == len(tongcheng_only_mappings):
        print("✓ 错误记录数量符合预期")
    else:
        print("✗ 错误记录数量不符合预期")
    
    return len(errors) == len(tongcheng_only_mappings)

if __name__ == "__main__":
    success = test_room_matching_logic()
    if success:
        print("\n🎉 测试通过！房型匹配逻辑修改成功。")
    else:
        print("\n❌ 测试失败！需要检查房型匹配逻辑。")
    
    sys.exit(0 if success else 1)

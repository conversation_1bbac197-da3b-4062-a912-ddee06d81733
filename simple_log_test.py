#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的日志统计测试
"""

print("=== 测试日志统计功能 ===")

# 模拟日志输出
def log_info(message):
    print(f"[INFO] {message}")

# 模拟统计数据
total_clicks = 330
hotel_data = {
    "新加坡费尔蒙酒店": {"clicks": 75, "rooms": 3, "room_details": {"豪华大床房": 25, "豪华港景房": 32, "特色套房": 18}},
    "香港半岛酒店": {"clicks": 73, "rooms": 2, "room_details": {"豪华客房": 28, "半岛套房": 45}},
    "东京帝国酒店": {"clicks": 182, "rooms": 4, "room_details": {"帝国客房": 22, "帝国套房": 38, "皇家套房": 55, "总统套房": 67}}
}

# 输出统计报告
log_info("=" * 60)
log_info("点击统计报告")
log_info("=" * 60)

log_info(f"总点击次数: {total_clicks}")
log_info(f"处理酒店数量: {len(hotel_data)}")

total_rooms = sum(data["rooms"] for data in hotel_data.values())
avg_clicks_per_hotel = total_clicks / len(hotel_data)
avg_clicks_per_room = total_clicks / total_rooms

log_info(f"平均每个酒店点击次数: {avg_clicks_per_hotel:.2f}")
log_info(f"平均每个房型点击次数: {avg_clicks_per_room:.2f}")

log_info("-" * 60)
log_info("各酒店点击统计详情")
log_info("-" * 60)

# 按点击次数排序
sorted_hotels = sorted(hotel_data.items(), key=lambda x: x[1]["clicks"], reverse=True)

for i, (hotel_name, data) in enumerate(sorted_hotels, 1):
    log_info(f"{i}. 酒店: {hotel_name}")
    log_info(f"   总点击次数: {data['clicks']}")
    log_info(f"   房型数量: {data['rooms']}")
    
    avg_clicks_per_room_hotel = data['clicks'] / data['rooms']
    log_info(f"   平均每房型点击: {avg_clicks_per_room_hotel:.2f}")
    
    log_info("   房型点击详情:")
    sorted_rooms = sorted(data["room_details"].items(), key=lambda x: x[1], reverse=True)
    for room_name, room_clicks in sorted_rooms:
        log_info(f"     - {room_name}: {room_clicks} 次")
    log_info("")

# 极值统计
max_hotel = max(hotel_data.items(), key=lambda x: x[1]["clicks"])
min_hotel = min(hotel_data.items(), key=lambda x: x[1]["clicks"])

log_info("-" * 60)
log_info("极值统计")
log_info("-" * 60)
log_info(f"点击次数最多的酒店: {max_hotel[0]} ({max_hotel[1]['clicks']} 次)")
log_info(f"点击次数最少的酒店: {min_hotel[0]} ({min_hotel[1]['clicks']} 次)")

# 找出点击次数最多和最少的房型
all_room_clicks = []
for hotel_name, data in hotel_data.items():
    for room_name, clicks in data["room_details"].items():
        all_room_clicks.append((room_name, clicks))

max_room = max(all_room_clicks, key=lambda x: x[1])
min_room = min(all_room_clicks, key=lambda x: x[1])
log_info(f"点击次数最多的房型: {max_room[0]} ({max_room[1]} 次)")
log_info(f"点击次数最少的房型: {min_room[0]} ({min_room[1]} 次)")

log_info("=" * 60)
log_info("统计报告结束")
log_info("=" * 60)

print("\n✓ 日志统计功能演示完成！")
print("✓ 所有统计信息都输出到日志中，不再生成JSON文件。")
print("✓ 统计报告包含详细的酒店和房型点击信息，以及极值分析。")

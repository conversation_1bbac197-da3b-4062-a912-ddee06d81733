"""
多标签页多进程功能测试脚本
验证系统的各个组件是否正常工作
"""

import sys
import time
import os
from typing import Dict, Any
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    DEFAULT_MULTI_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG,
    SINGLE_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import (
    MultiTabConfig,
    DEFAULT_CONFIG,
    FAST_MODE_CONFIG,
    SAFE_MODE_CONFIG,
    SINGLE_TAB_CONFIG,
    MULTI_PROCESS_TAB_CONFIG
)
from tongcheng.workflow.multi_tab_performance_monitor import MultiTabPerformanceMonitor
from tongcheng.tools.logger import get_workflow_logger

logger = get_workflow_logger()


def print_test_header(test_name: str):
    """打印测试标题"""
    print("\n" + "=" * 80)
    print(f"🧪 {test_name}")
    print("=" * 80)


def print_test_result(test_name: str, success: bool, message: str = ""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if message:
        print(f"   {message}")


def test_config_creation():
    """测试配置创建"""
    print_test_header("配置创建测试")
    
    tests = [
        ("默认多标签页配置", lambda: DEFAULT_CONFIG),
        ("快速模式多标签页配置", lambda: FAST_MODE_CONFIG),
        ("安全模式多标签页配置", lambda: SAFE_MODE_CONFIG),
        ("单标签页配置", lambda: SINGLE_TAB_CONFIG),
        ("多进程专用标签页配置", lambda: MULTI_PROCESS_TAB_CONFIG),
        ("默认多进程配置", lambda: DEFAULT_MULTI_PROCESS_CONFIG),
        ("安全模式多进程配置", lambda: SAFE_MULTI_PROCESS_CONFIG),
        ("快速模式多进程配置", lambda: FAST_MULTI_PROCESS_CONFIG),
        ("单进程配置", lambda: SINGLE_PROCESS_CONFIG),
    ]
    
    all_passed = True
    
    for test_name, config_func in tests:
        try:
            config = config_func()
            
            # 验证配置有效性
            if isinstance(config, MultiTabConfig):
                valid = config.validate()
                print_test_result(test_name, valid, f"标签页数: {config.max_tabs}")
            elif isinstance(config, MultiProcessConfig):
                valid = (
                    config.max_processes > 0 and
                    config.multi_tab_config is not None and
                    config.multi_tab_config.validate()
                )
                parallel = config.max_processes * config.multi_tab_config.max_tabs
                print_test_result(test_name, valid, f"并行度: {parallel}x")
            else:
                valid = False
                print_test_result(test_name, False, "配置类型错误")
            
            if not valid:
                all_passed = False
                
        except Exception as e:
            print_test_result(test_name, False, f"异常: {str(e)}")
            all_passed = False
    
    return all_passed


def test_custom_config_creation():
    """测试自定义配置创建"""
    print_test_header("自定义配置创建测试")
    
    try:
        # 创建自定义多标签页配置
        custom_tab_config = MultiTabConfig(
            max_tabs=4,
            enable_multi_tab=True,
            room_interval=1.5,
            headless=True,
            enable_detailed_logging=True
        )
        
        valid_tab = custom_tab_config.validate()
        print_test_result("自定义多标签页配置", valid_tab, f"4标签页, 无头模式")
        
        # 创建自定义多进程配置
        custom_process_config = MultiProcessConfig(
            max_processes=3,
            enable_multi_process=True,
            multi_tab_config=custom_tab_config,
            hotel_interval=2.0
        )
        
        valid_process = (
            custom_process_config.max_processes > 0 and
            custom_process_config.multi_tab_config is not None
        )
        
        parallel = custom_process_config.max_processes * custom_process_config.multi_tab_config.max_tabs
        print_test_result("自定义多进程配置", valid_process, f"3进程 × 4标签页 = {parallel}x并行度")
        
        return valid_tab and valid_process
        
    except Exception as e:
        print_test_result("自定义配置创建", False, f"异常: {str(e)}")
        return False


def test_performance_monitor():
    """测试性能监控器"""
    print_test_header("性能监控器测试")
    
    try:
        monitor = MultiTabPerformanceMonitor()
        
        # 测试启动监控
        monitor.start_monitoring(total_processes=2, tabs_per_process=3)
        print_test_result("启动性能监控", True, "2进程 × 3标签页")
        
        # 模拟一些活动
        monitor.record_process_start(process_id=1001, tab_count=3)
        monitor.record_process_start(process_id=1002, tab_count=3)
        
        time.sleep(1)  # 等待一秒
        
        # 模拟处理活动
        monitor.record_hotel_processed(process_id=1001, success=True, processing_time=5.0, room_count=3)
        monitor.record_hotel_processed(process_id=1002, success=True, processing_time=4.5, room_count=2)
        monitor.record_hotel_processed(process_id=1001, success=False, processing_time=2.0, room_count=1)
        
        # 模拟标签页活动
        monitor.record_tab_activity(process_id=1001, tab_id=0, room_success=True, processing_time=1.5)
        monitor.record_tab_activity(process_id=1001, tab_id=1, room_success=True, processing_time=1.8)
        monitor.record_tab_activity(process_id=1002, tab_id=0, room_success=False, processing_time=2.0)
        
        # 模拟错误
        monitor.record_error(process_id=1001, error_type="连接超时", error_message="无法连接到服务器")
        
        # 获取实时统计
        real_time_stats = monitor.get_real_time_stats()
        print_test_result("实时统计获取", True, f"处理了 {real_time_stats['system_metrics']['total_hotels']} 个酒店")
        
        # 生成性能报告
        report = monitor.generate_performance_report()
        print_test_result("性能报告生成", True, f"成功率: {report['summary']['overall_success_rate']:.1f}%")
        
        # 停止监控
        monitor.stop_monitoring()
        print_test_result("停止性能监控", True)
        
        return True
        
    except Exception as e:
        print_test_result("性能监控器测试", False, f"异常: {str(e)}")
        return False


def test_config_compatibility():
    """测试配置兼容性"""
    print_test_header("配置兼容性测试")
    
    try:
        # 测试不同配置组合
        combinations = [
            ("单进程 + 单标签页", SINGLE_PROCESS_CONFIG),
            ("单进程 + 多标签页", MultiProcessConfig(
                max_processes=1,
                enable_multi_process=False,
                multi_tab_config=DEFAULT_CONFIG
            )),
            ("多进程 + 单标签页", MultiProcessConfig(
                max_processes=2,
                enable_multi_process=True,
                multi_tab_config=SINGLE_TAB_CONFIG
            )),
            ("多进程 + 多标签页", DEFAULT_MULTI_PROCESS_CONFIG),
        ]
        
        all_passed = True
        
        for name, config in combinations:
            try:
                # 验证配置逻辑一致性
                is_multi_process = config.enable_multi_process and config.max_processes > 1
                is_multi_tab = config.multi_tab_config.enable_multi_tab and config.multi_tab_config.max_tabs > 1
                
                parallel = config.max_processes * config.multi_tab_config.max_tabs
                
                # 检查配置合理性
                valid = (
                    config.max_processes > 0 and
                    config.multi_tab_config.max_tabs > 0 and
                    parallel <= 50  # 合理的并行度上限
                )
                
                mode_desc = f"{'多进程' if is_multi_process else '单进程'} + {'多标签页' if is_multi_tab else '单标签页'}"
                print_test_result(name, valid, f"{mode_desc}, 并行度: {parallel}x")
                
                if not valid:
                    all_passed = False
                    
            except Exception as e:
                print_test_result(name, False, f"异常: {str(e)}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print_test_result("配置兼容性测试", False, f"异常: {str(e)}")
        return False


def test_system_requirements():
    """测试系统要求检查"""
    print_test_header("系统要求检查测试")
    
    try:
        # 检查必要的模块导入
        modules_to_test = [
            ("多进程协调器", "tongcheng.workflow.multi_process_orchestrator"),
            ("进程管理器", "tongcheng.workflow.process_manager"),
            ("多标签页处理器", "tongcheng.pusher.tongcheng.multi_tab_processor"),
            ("性能监控器", "tongcheng.workflow.multi_tab_performance_monitor"),
        ]
        
        all_passed = True
        
        for name, module_name in modules_to_test:
            try:
                __import__(module_name)
                print_test_result(f"{name}模块导入", True)
            except ImportError as e:
                print_test_result(f"{name}模块导入", False, f"导入失败: {str(e)}")
                all_passed = False
            except Exception as e:
                print_test_result(f"{name}模块导入", False, f"异常: {str(e)}")
                all_passed = False
        
        # 检查Cookie文件（可选）
        cookie_files = ["cookies.json", "tongcheng_cookies.json"]
        cookie_found = False
        
        for cookie_file in cookie_files:
            if os.path.exists(cookie_file):
                cookie_found = True
                print_test_result("Cookie文件检查", True, f"找到: {cookie_file}")
                break
        
        if not cookie_found:
            print_test_result("Cookie文件检查", False, "未找到Cookie文件（运行时需要）")
        
        # 检查日志目录
        log_dirs = ["logs", "tongcheng/logs"]
        for log_dir in log_dirs:
            if os.path.exists(log_dir):
                print_test_result("日志目录检查", True, f"找到: {log_dir}")
                break
        else:
            print_test_result("日志目录检查", False, "未找到日志目录（将自动创建）")
        
        return all_passed
        
    except Exception as e:
        print_test_result("系统要求检查", False, f"异常: {str(e)}")
        return False


def test_configuration_validation():
    """测试配置验证"""
    print_test_header("配置验证测试")
    
    try:
        # 测试有效配置
        valid_configs = [
            ("最小配置", MultiTabConfig(max_tabs=1, enable_multi_tab=False)),
            ("标准配置", MultiTabConfig(max_tabs=3, enable_multi_tab=True)),
            ("高性能配置", MultiTabConfig(max_tabs=8, enable_multi_tab=True)),
        ]
        
        # 测试无效配置
        invalid_configs = [
            ("零标签页", MultiTabConfig(max_tabs=0)),
            ("负数标签页", MultiTabConfig(max_tabs=-1)),
            ("负超时时间", MultiTabConfig(tab_acquire_timeout=-1)),
            ("负重试次数", MultiTabConfig(max_retries=-1)),
        ]
        
        all_passed = True
        
        # 验证有效配置
        for name, config in valid_configs:
            valid = config.validate()
            print_test_result(f"有效配置: {name}", valid)
            if not valid:
                all_passed = False
        
        # 验证无效配置
        for name, config in invalid_configs:
            valid = config.validate()
            print_test_result(f"无效配置: {name}", not valid, "应该验证失败")
            if valid:  # 如果无效配置通过了验证，说明验证逻辑有问题
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print_test_result("配置验证测试", False, f"异常: {str(e)}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 多标签页多进程功能测试套件")
    print("=" * 80)
    print("测试系统各个组件的功能和兼容性")
    print()
    
    start_time = time.time()
    
    tests = [
        ("配置创建", test_config_creation),
        ("自定义配置创建", test_custom_config_creation),
        ("性能监控器", test_performance_monitor),
        ("配置兼容性", test_config_compatibility),
        ("系统要求检查", test_system_requirements),
        ("配置验证", test_configuration_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 生成测试报告
    print("\n" + "📊" * 30)
    print("测试结果汇总")
    print("📊" * 30)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    success_rate = passed_count / total_count * 100
    
    print(f"总测试数: {total_count}")
    print(f"通过测试: {passed_count}")
    print(f"失败测试: {total_count - passed_count}")
    print(f"成功率: {success_rate:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    total_time = time.time() - start_time
    print(f"\n总耗时: {total_time:.2f}秒")
    
    if success_rate == 100:
        print("\n🎉 所有测试通过！系统准备就绪。")
    elif success_rate >= 80:
        print("\n⚠️ 大部分测试通过，系统基本可用。")
    else:
        print("\n❌ 多个测试失败，请检查系统配置。")
    
    return success_rate == 100


def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n🚀 测试完成，可以运行主程序:")
            print("   python main_multi_tab_multi_process.py")
        else:
            print("\n🔧 请修复测试失败的问题后再运行主程序")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试执行异常: {str(e)}")
        logger.error(f"测试执行异常: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

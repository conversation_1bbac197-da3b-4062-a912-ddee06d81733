"""
多标签页多进程房型处理示例
演示如何使用新的多标签页架构处理多个房型
"""

import time
import json
from typing import Dict, List
from tongcheng.pusher.tongcheng.multi_tab_processor import MultiTabTongchengPusher
from tongcheng.config.multi_tab_config import MultiTabConfig, FAST_MODE_CONFIG, SAFE_MODE_CONFIG
from tongcheng.tools.logger import get_pusher_logger

logger = get_pusher_logger()


def create_sample_hotel_data() -> Dict:
    """创建示例酒店数据"""
    return {
        "同程酒店名称": "香港丽思卡尔顿酒店",
        "房型数据": [
            {
                "同程房型名称": "豪华大床房（港景，带阳台）",
                "八大洲房型名称": "豪华客房(大床)",
                "八大洲房型编码": "ROOM001",
                "价格数据": [
                    {
                        "2025-07-07": "1200.00",
                        "2025-07-08": "1250.00",
                        "2025-07-09": "1300.00",
                        "2025-07-10": "1350.00",
                        "2025-07-11": "1400.00"
                    }
                ]
            },
            {
                "同程房型名称": "豪华双床房（港景）",
                "八大洲房型名称": "豪华客房(双床)",
                "八大洲房型编码": "ROOM002",
                "价格数据": [
                    {
                        "2025-07-07": "1100.00",
                        "2025-07-08": "1150.00",
                        "2025-07-09": "1200.00",
                        "2025-07-10": "1250.00",
                        "2025-07-11": "1300.00"
                    }
                ]
            },
            {
                "同程房型名称": "行政套房（海景）",
                "八大洲房型名称": "行政套房",
                "八大洲房型编码": "ROOM003",
                "价格数据": [
                    {
                        "2025-07-07": "2000.00",
                        "2025-07-08": "2100.00",
                        "2025-07-09": "2200.00",
                        "2025-07-10": "2300.00",
                        "2025-07-11": "2400.00"
                    }
                ]
            },
            {
                "同程房型名称": "总统套房",
                "八大洲房型名称": "总统套房",
                "八大洲房型编码": "ROOM004",
                "价格数据": [
                    {
                        "2025-07-07": "5000.00",
                        "2025-07-08": "5200.00",
                        "2025-07-09": "5400.00",
                        "2025-07-10": "5600.00",
                        "2025-07-11": "5800.00"
                    }
                ]
            }
        ]
    }


def demo_single_tab_vs_multi_tab():
    """演示单标签页与多标签页的性能对比"""
    print("=" * 60)
    print("🚀 多标签页多进程房型处理演示")
    print("=" * 60)
    
    hotel_data = create_sample_hotel_data()
    hotel_name = hotel_data["同程酒店名称"]
    room_count = len(hotel_data["房型数据"])
    
    print(f"📊 测试数据:")
    print(f"   酒店: {hotel_name}")
    print(f"   房型数量: {room_count}")
    print()
    
    # 演示不同配置模式
    configs = {
        "安全模式": SAFE_MODE_CONFIG,
        "快速模式": FAST_MODE_CONFIG,
        "默认模式": MultiTabConfig.create_default()
    }
    
    for mode_name, config in configs.items():
        print(f"📋 {mode_name} 配置:")
        print(f"   最大标签页: {config.max_tabs}")
        print(f"   房型间隔: {config.room_interval}秒")
        print(f"   标签页切换延迟: {config.tab_switch_delay}秒")
        print(f"   获取标签页超时: {config.tab_acquire_timeout}秒")
        print()


def run_multi_tab_processing():
    """运行多标签页处理"""
    print("🔧 开始多标签页处理演示...")
    
    # 选择配置模式
    print("请选择处理模式:")
    print("1. 安全模式 (2个标签页，较慢但稳定)")
    print("2. 默认模式 (3个标签页，平衡性能)")
    print("3. 快速模式 (5个标签页，最快但可能不稳定)")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        config = SAFE_MODE_CONFIG
        mode_name = "安全模式"
    elif choice == "3":
        config = FAST_MODE_CONFIG
        mode_name = "快速模式"
    else:
        config = MultiTabConfig.create_default()
        mode_name = "默认模式"
    
    print(f"\n✅ 选择了 {mode_name}")
    print(f"   标签页数量: {config.max_tabs}")
    print(f"   预计并行度: {config.max_tabs}x")
    
    # 创建多标签页推送器
    pusher = MultiTabTongchengPusher(
        cookies_path="cookies.json",
        headless=config.headless,
        max_tabs=config.max_tabs
    )
    
    try:
        # 连接并初始化
        print(f"\n🔗 正在初始化 {config.max_tabs} 个标签页...")
        start_time = time.time()
        
        if not pusher.connect():
            print("❌ 连接失败")
            return False
        
        init_time = time.time() - start_time
        print(f"✅ 初始化完成，耗时: {init_time:.2f}秒")
        
        # 处理酒店数据
        hotel_data = create_sample_hotel_data()
        
        print(f"\n🏨 开始处理酒店: {hotel_data['同程酒店名称']}")
        print(f"📋 房型数量: {len(hotel_data['房型数据'])}")
        
        processing_start = time.time()
        
        # 执行多标签页处理
        success = pusher.push_price(hotel_data)
        
        processing_time = time.time() - processing_start
        total_time = time.time() - start_time
        
        # 显示结果
        print(f"\n📊 处理结果:")
        print(f"   状态: {'✅ 成功' if success else '❌ 失败'}")
        print(f"   处理时间: {processing_time:.2f}秒")
        print(f"   总时间: {total_time:.2f}秒")
        print(f"   平均每房型: {processing_time / len(hotel_data['房型数据']):.2f}秒")
        
        if success:
            print(f"\n🎉 多标签页处理成功!")
            print(f"   相比单标签页，理论提速: {config.max_tabs}x")
        else:
            print(f"\n⚠️ 处理过程中出现问题，请检查日志")
        
        return success
        
    except Exception as e:
        print(f"❌ 处理过程中发生异常: {str(e)}")
        return False
        
    finally:
        # 清理资源
        print(f"\n🧹 清理资源...")
        pusher.disconnect()
        print("✅ 清理完成")


def compare_processing_modes():
    """比较不同处理模式的理论性能"""
    print("\n" + "=" * 60)
    print("📈 处理模式性能对比分析")
    print("=" * 60)
    
    room_count = 4  # 示例房型数量
    single_room_time = 30  # 假设单个房型处理时间30秒
    
    modes = {
        "单标签页模式": {"tabs": 1, "config": MultiTabConfig.create_single_tab_mode()},
        "安全模式": {"tabs": 2, "config": SAFE_MODE_CONFIG},
        "默认模式": {"tabs": 3, "config": MultiTabConfig.create_default()},
        "快速模式": {"tabs": 5, "config": FAST_MODE_CONFIG}
    }
    
    print(f"假设条件:")
    print(f"  房型数量: {room_count}")
    print(f"  单房型处理时间: {single_room_time}秒")
    print()
    
    for mode_name, mode_info in modes.items():
        tabs = mode_info["tabs"]
        config = mode_info["config"]
        
        # 计算理论处理时间
        if tabs == 1:
            # 单标签页：顺序处理
            total_time = room_count * single_room_time
            parallel_efficiency = 1.0
        else:
            # 多标签页：并行处理
            batches = (room_count + tabs - 1) // tabs  # 向上取整
            total_time = batches * single_room_time
            parallel_efficiency = room_count / (batches * tabs)
        
        speedup = (room_count * single_room_time) / total_time
        
        print(f"📋 {mode_name}:")
        print(f"   标签页数: {tabs}")
        print(f"   理论处理时间: {total_time}秒 ({total_time/60:.1f}分钟)")
        print(f"   加速比: {speedup:.1f}x")
        print(f"   并行效率: {parallel_efficiency:.1%}")
        print(f"   房型间隔: {config.room_interval}秒")
        print()


def main():
    """主函数"""
    print("🎯 同程酒店多标签页多进程处理系统")
    print("解决多房型处理时的状态冲突问题，大幅提升处理效率")
    print()
    
    while True:
        print("请选择操作:")
        print("1. 查看多标签页架构说明")
        print("2. 比较不同处理模式性能")
        print("3. 运行多标签页处理演示")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            demo_single_tab_vs_multi_tab()
        elif choice == "2":
            compare_processing_modes()
        elif choice == "3":
            if input("\n⚠️ 这将启动真实的浏览器进行处理，确认继续？(y/N): ").strip().lower() == 'y':
                run_multi_tab_processing()
            else:
                print("已取消")
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")
        
        print("\n" + "-" * 60 + "\n")


if __name__ == "__main__":
    main()

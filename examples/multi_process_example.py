#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程酒店价格同步系统 - 多进程多标签页使用示例

展示如何使用多进程多标签页版本进行酒店价格处理
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    SAFE_MULTI_PROCESS_CONFIG,
    DEFAULT_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG,
    SINGLE_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import MultiTabConfig


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("📚 基本使用示例")
    print("=" * 60)
    
    # 使用默认配置
    config = DEFAULT_MULTI_PROCESS_CONFIG
    
    print(f"配置信息:")
    print(f"  进程数: {config.max_processes}")
    print(f"  每进程标签页数: {config.multi_tab_config.max_tabs}")
    print(f"  理论并行度: {config.max_processes * config.multi_tab_config.max_tabs}x")
    
    # 创建协调器
    orchestrator = MultiProcessOrchestrator(config)
    
    # 执行健康检查
    health = orchestrator.health_check()
    print(f"\n系统健康状态: {'✅ 健康' if health['healthy'] else '❌ 异常'}")
    
    if not health['healthy']:
        print(f"健康检查失败: {health}")
        return
    
    print(f"\n🚀 开始执行多进程工作流程...")
    start_time = time.time()
    
    try:
        # 执行主工作流程
        result = orchestrator.main_workflow()
        
        execution_time = time.time() - start_time
        
        # 显示结果
        print(f"\n📊 执行完成，耗时: {execution_time:.2f}秒")
        
        if result["status"] == "success":
            summary = result["summary"]
            print(f"✅ 处理成功!")
            print(f"  处理酒店: {summary['success_hotels']}/{summary['total_hotels']} 个")
            print(f"  成功率: {summary['success_rate']:.1f}%")
            print(f"  处理房型: {summary['total_rooms_processed']} 个")
            print(f"  平均每酒店: {summary['average_processing_time']:.2f}秒")
        else:
            print(f"⚠️ 处理完成但有问题: {result['message']}")
            
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ 执行异常，耗时: {execution_time:.2f}秒")
        print(f"错误信息: {str(e)}")


def example_custom_config():
    """自定义配置示例"""
    print("=" * 60)
    print("🔧 自定义配置示例")
    print("=" * 60)
    
    # 创建自定义多标签页配置
    custom_tab_config = MultiTabConfig(
        max_tabs=4,                    # 4个标签页
        enable_multi_tab=True,
        tab_acquire_timeout=45.0,      # 获取标签页超时45秒
        room_processing_timeout=240.0, # 房型处理超时4分钟
        headless=False,                # 显示浏览器界面
        enable_detailed_logging=True   # 启用详细日志
    )
    
    # 创建自定义多进程配置
    custom_config = MultiProcessConfig(
        max_processes=2,                      # 2个进程
        enable_multi_process=True,
        multi_tab_config=custom_tab_config,
        process_startup_timeout=120.0,        # 进程启动超时2分钟
        hotel_processing_timeout=1800.0,      # 酒店处理超时30分钟
        max_process_retries=2,                # 最大重试2次
        hotel_interval=3.0,                   # 酒店间隔3秒
        enable_process_logging=True           # 启用进程日志
    )
    
    print(f"自定义配置:")
    print(f"  进程数: {custom_config.max_processes}")
    print(f"  标签页数: {custom_config.multi_tab_config.max_tabs}")
    print(f"  进程启动超时: {custom_config.process_startup_timeout}秒")
    print(f"  酒店处理超时: {custom_config.hotel_processing_timeout}秒")
    print(f"  无头模式: {custom_config.multi_tab_config.headless}")
    
    # 创建协调器
    orchestrator = MultiProcessOrchestrator(custom_config)
    
    # 获取系统状态
    status = orchestrator.get_system_status()
    print(f"\n系统状态:")
    print(f"  多进程配置: {status['multi_process_config']}")
    print(f"  错误数量: {status['error_count']}")


def example_performance_comparison():
    """性能对比示例"""
    print("=" * 60)
    print("📈 性能对比示例")
    print("=" * 60)
    
    configs = {
        "单进程模式": SINGLE_PROCESS_CONFIG,
        "安全模式": SAFE_MULTI_PROCESS_CONFIG,
        "默认模式": DEFAULT_MULTI_PROCESS_CONFIG,
        "快速模式": FAST_MULTI_PROCESS_CONFIG
    }
    
    # 假设处理参数
    hotel_count = 4
    room_per_hotel = 3
    time_per_room = 30  # 秒
    
    print(f"假设场景: {hotel_count}个酒店, 每酒店{room_per_hotel}个房型, 每房型{time_per_room}秒")
    print("-" * 60)
    print(f"{'模式':<12} {'进程':<4} {'标签页':<6} {'并行度':<6} {'预计时间':<10} {'加速比':<8}")
    print("-" * 60)
    
    baseline_time = hotel_count * room_per_hotel * time_per_room
    
    for mode_name, config in configs.items():
        processes = config.max_processes
        tabs = config.multi_tab_config.max_tabs
        parallelism = processes * tabs
        
        # 简化的时间计算
        if processes == 1 and tabs == 1:
            estimated_time = baseline_time
        else:
            # 考虑进程和标签页的并行效果
            hotels_per_process = max(1, hotel_count / processes)
            rooms_per_tab = max(1, room_per_hotel / tabs)
            estimated_time = hotels_per_process * rooms_per_tab * time_per_room
        
        speedup = baseline_time / estimated_time if estimated_time > 0 else 1.0
        
        print(f"{mode_name:<12} {processes:<4} {tabs:<6} {parallelism:<6} {estimated_time/60:.1f}分钟{'':<3} {speedup:.1f}x")
    
    print("-" * 60)
    print("💡 注意: 实际性能受网络、系统资源等因素影响")


def example_error_handling():
    """错误处理示例"""
    print("=" * 60)
    print("🛠️ 错误处理示例")
    print("=" * 60)
    
    # 创建配置，设置较短的超时时间来演示错误处理
    config = MultiProcessConfig(
        max_processes=1,
        process_startup_timeout=5.0,    # 很短的超时时间
        hotel_processing_timeout=10.0,  # 很短的处理时间
        max_process_retries=1,          # 只重试1次
        multi_tab_config=MultiTabConfig(
            max_tabs=1,
            room_processing_timeout=5.0  # 很短的房型处理时间
        )
    )
    
    print(f"使用短超时配置演示错误处理:")
    print(f"  进程启动超时: {config.process_startup_timeout}秒")
    print(f"  酒店处理超时: {config.hotel_processing_timeout}秒")
    print(f"  最大重试次数: {config.max_process_retries}")
    
    orchestrator = MultiProcessOrchestrator(config)
    
    try:
        print(f"\n🚀 开始执行（预期会有超时错误）...")
        result = orchestrator.main_workflow()
        
        if result["status"] == "success":
            print(f"✅ 意外成功了!")
        else:
            print(f"⚠️ 如预期出现问题: {result['message']}")
            
        # 显示错误信息
        if "execution_report" in result:
            error_summary = result["execution_report"].get("error_summary", {})
            print(f"\n错误统计:")
            print(f"  总错误数: {error_summary.get('total_errors', 0)}")
            
            recent_errors = error_summary.get('recent_errors', [])
            if recent_errors:
                print(f"  最近错误:")
                for error in recent_errors[-3:]:  # 显示最近3个错误
                    print(f"    - {error.get('error_type', 'Unknown')}: {error.get('details', {})}")
                    
    except Exception as e:
        print(f"❌ 捕获到异常: {str(e)}")
        print(f"这是正常的错误处理演示")


def example_monitoring():
    """监控示例"""
    print("=" * 60)
    print("📊 监控示例")
    print("=" * 60)
    
    # 创建启用监控的配置
    config = MultiProcessConfig(
        max_processes=2,
        enable_process_logging=True,
        log_process_metrics=True,
        multi_tab_config=MultiTabConfig(
            max_tabs=2,
            enable_detailed_logging=True,
            log_performance_metrics=True
        )
    )
    
    print(f"监控配置:")
    print(f"  进程日志: {config.enable_process_logging}")
    print(f"  进程指标: {config.log_process_metrics}")
    print(f"  详细日志: {config.multi_tab_config.enable_detailed_logging}")
    print(f"  性能指标: {config.multi_tab_config.log_performance_metrics}")
    
    orchestrator = MultiProcessOrchestrator(config)
    
    # 获取系统状态
    status = orchestrator.get_system_status()
    print(f"\n当前系统状态:")
    print(f"  多进程配置: {status['multi_process_config']}")
    print(f"  性能统计: {status.get('performance_stats', {})}")
    print(f"  错误数量: {status['error_count']}")
    
    # 执行健康检查
    health = orchestrator.health_check()
    print(f"\n健康检查结果:")
    print(f"  整体健康: {health['healthy']}")
    print(f"  组件状态: {health.get('components', {})}")
    print(f"  配置有效: {health.get('config_valid', False)}")


def main():
    """主函数"""
    print("🎯 同程酒店多进程多标签页处理系统 - 使用示例")
    print("展示各种使用场景和配置方法")
    print()
    
    examples = {
        "1": ("基本使用示例", example_basic_usage),
        "2": ("自定义配置示例", example_custom_config),
        "3": ("性能对比示例", example_performance_comparison),
        "4": ("错误处理示例", example_error_handling),
        "5": ("监控示例", example_monitoring)
    }
    
    while True:
        print("请选择要运行的示例:")
        for key, (name, _) in examples.items():
            print(f"{key}. {name}")
        print("6. 运行所有示例")
        print("7. 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice in examples:
            name, func = examples[choice]
            print(f"\n🚀 运行 {name}...")
            try:
                func()
            except Exception as e:
                print(f"❌ 示例执行异常: {str(e)}")
            print(f"\n✅ {name} 完成")
            
        elif choice == "6":
            print(f"\n🚀 运行所有示例...")
            for name, func in examples.values():
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    func()
                except Exception as e:
                    print(f"❌ {name} 执行异常: {str(e)}")
                print(f"✅ {name} 完成")
            print(f"\n🎉 所有示例运行完成!")
            
        elif choice == "7":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")
        
        print("\n" + "-" * 80 + "\n")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n👋 示例程序被用户中断")
    except Exception as e:
        print(f"\n❌ 示例程序异常: {str(e)}")

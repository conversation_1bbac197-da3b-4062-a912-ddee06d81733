import requests
import json


headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=4659fd630fa54e4caa5986aa846b0a40,sentry-sample_rate=0.1,sentry-transaction=%2Febk-room%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "4659fd630fa54e4caa5986aa846b0a40-8c27ac6ddf52d9a3-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxIt2ktctxIidjlsvkSkiGgsCYhl8dWX7o85pxEzK050SIi80Mx70ntTOXx7xtDGg0UUB7ynxSq+rpu0SKlNzcdZhrMGf796Y2jHF+KbbPOcahoSjhvuzVvTLHjCzoexRAzMSLo/WjNc51u0HQRIJzq/NCMiYnK0WWsyrrCsBApOzhYdPrForSCsBACgzFqaeRvnSkqPg3SwcQzmSVc0CynNudBTY3Cvc7T2Txstj+aIvhSyiF16ZEHnZbpekKV+thzU8Bqgf1yL0772StceCNSsZKi4bDPKsUkUxP+1gCrF3TG9mBN25Fop3fV9QtmdB9DZQ8mw3QV9OGmU9ft9WtmKOUK22SYKTr4zrLG9WV8UPxviancF1vt9hLMUEq6JBJrK4TGZgV2FZMG9fGnZUWl9VCj6TTyGOVO2SvnmsMeJsdpR+3pH7W6k7DTvpQcmF5lrpb6rG5lrtber73cHKXQg+Z+gslsJu5bi3TG9JCPi3TGGaj3m5wIx+Jax8Hi5j="
}
cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "1754eab5519eed1cbed1f94e8cdcc1e7",
    "lasttime": "1750382711556"
}
url = "https://ebooking.elong.com/ebkroom/hotelManage/getMRoomTypes"
data = {
    "shotelId": "26964091"
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)
import requests
import json


headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=789d74928d5e41438b72b7dc38fc87a2,sentry-sample_rate=0.1,sentry-transaction=%2Febk-room%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "789d74928d5e41438b72b7dc38fc87a2-9503a5d15466cf1c-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxI9ABzLxxIVn0bakNzA2GgsCYhl8dWX7o85pxEzK050SIi80Mx70ntTOUx7xCSmvtPvC/PnRLP0VB7nCini0Bfn3Xg9QR8hQrJinKeBBezaYleB200iYT+vQuzVLK3f5dLlutTm2BmqYY6ehiDQz/PnCDUQVI7Y7fPbEVP4CsjEVdcEVK2tCTKcXUpXtUMJV2wxlw7RaOPvVd7INBUsu1XXOC3miB5LLU3ZDe7PnX3gWJwfpFfPJ05Lpj8700G8auPEVBPERdh4VB1LCMnkNUbPOzSPu1NEW0YvAF/NhkpSxaxhgn+z7Xix/+cPIO84z2BhFC3VuGeUhM+8Bl3rHFIfxMK5t+tQWXKEx2UzspjPQY9dmeO5My7kEIoJH2tibNCdD3lV3JnJH2CwvJ20zK792sw3WUCM46MyNN51/TKoJXkskuKuv2p6K9j3W2KOhzcFxXSoZuKukqvn4AiNwYvNzrivW3Nz07eTBLx5QNKImbONTzKkt2shVsa7C9i7=="
}
cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "1754eab5519eed1cbed1f94e8cdcc1e7",
    "lasttime": "1750382711556"
}
url = "https://ebooking.elong.com/ebkorder/searchCondition/selectHotelsByName"
data = {
    "provinceId": "",
    "hotelName": "新加坡艾迪逊酒店"
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)
from DrissionPage import ChromiumPage
import json
import time

# 目标页面
url = 'https://ebooking.elong.com/ebkcommon/dashboard#/dashboard'
# cookies 文件路径
cookies_path = 'cookies.json'

# 读取 cookies.json 文件
with open(cookies_path, 'r', encoding='utf-8') as f:
    cookies = json.load(f)

# 启动 ChromiumPage
page = ChromiumPage()

# 先访问主域，便于设置 cookies
page.get('https://ebooking.elong.com')

# 设置 cookies（注：此处 cookies 为列表，每个元素为 dict，符合 DrissionPage 要求）
page.set.cookies(cookies)

# 再访问目标页面
page.get(url)

# 保持页面打开，防止脚本退出（可根据需要调整时间）
time.sleep(60)


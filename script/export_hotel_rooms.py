import requests
import json
import pandas as pd
from datetime import datetime, timedelta

# 新增功能：酒店名与hotelCode的映射
hotel_list = [
    ("香港尼依格罗美利酒店", "klafmd482018"),
    ("多哈悦榕庄酒店", "woqxxa682525"),
    ("新加坡COMO大都会酒店", "cotoco047916"),
    ("阿布扎比艾迪逊酒店", "gcvyxz980399"),
    ("吉隆坡威斯汀酒店", "otkjyx939436"),
    ("马尼拉莱佛士酒店", "jcfbfl629733"),
    ("曼谷瑰丽酒店", "lilogk959900"),
    ("曼谷文华东方酒店", "kkpcxe204158"),
    ("曼谷柏悦酒店", "jgaeek138220"),
    ("清迈美平洲际酒店", "hziyuq409075"),
    ("香港港丽酒店（康莱德）", "huwipk843531"),
    ("香港丽思卡尔顿酒店", "ucjawg449284"),
    ("香港半岛酒店", "utjzpo034485"),
    ("港岛香格里拉大酒店", "fxrpnt289934"),
    ("美憬阁普吉岛V别墅酒店", "cjjlbc422849"),
    ("黄金海岸索菲特宽滩酒店", "qozddq652961"),
    ("新加坡仕丹德酒店", "lcorgz427664"),
    ("新加坡艾迪逊酒店", "fshjql959076"),
    ("蒙德里安新加坡达士岭酒店", "ijkiom678550"),
    ("苏梅岛金普顿基塔莱酒店", "bjzehm747246"),
    ("迪拜棕榈莱佛士酒店", "zpwrwf792776"),
    ("卓美亚帆船酒店", "qetbfj068059"),
    ("曼谷维伊 - 美憬阁酒店", "nrgbsu355254"),
    ("曼谷湄南河四季酒店", "cbzatt238793"),
    ("曼谷嘉佩乐酒店", "funoup289962"),
    ("曼谷半岛酒店", "vkeuxw546474"),
    ("曼谷W酒店", "zondxj045470"),
    ("新加坡嘉佩乐酒店", "wphdol986023"),
    ("新加坡费尔蒙酒店", "kvqwsi452900"),
]

# 新增功能：接口请求参数
url = "http://139.199.168.35:8080/getPrice"
headers = {'Content-Type': 'application/json'}

# 新增功能：获取未来30天日期
start_date = datetime.today()
date_list = [(start_date + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30)]

# 新增功能：收集所有酒店的房型信息
excel_writer = pd.ExcelWriter('hotel_rooms.xlsx', engine='openpyxl')

for hotel_name, hotel_code in hotel_list:
    room_dict = {}  # 按roomCode去重
    for check_in in date_list:
        check_out = (datetime.strptime(check_in, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        payload = json.dumps({
            "hotelCode": hotel_code,
            "checkInDate": check_in,
            "checkOutDate": check_out,
            "personCount": "1",
            "roomCount": "1",
            "channel": "同城"
        })
        try:
            response = requests.request("GET", url, headers=headers, data=payload, timeout=10)
            resp_json = json.loads(response.text)
            data = resp_json.get('data', {})
            for room in data.get("roomList", []):
                room_code = room.get("roomCode", "")
                room_name = room.get("roomName", "")
                if room_code and room_code not in room_dict:
                    room_dict[room_code] = room_name
        except Exception as e:
            print(f"{hotel_name} {check_in} 查询出错: {e}")
    # 新增功能：写入Excel sheet
    df = pd.DataFrame([
        {"roomCode": code, "roomName": name} for code, name in room_dict.items()
    ])
    df.to_excel(excel_writer, sheet_name=hotel_name[:31], index=False)  # sheet名最长31字符
    print(f"{hotel_name} 完成, 共{len(df)}个房型")

excel_writer.close()
print("所有酒店房型信息已写入 hotel_rooms.xlsx") 
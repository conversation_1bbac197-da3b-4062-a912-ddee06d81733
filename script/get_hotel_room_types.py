import requests
import json
import pandas as pd
import time

# --- 配置信息 ---

# 注意：headers 和 cookies 可能有有效期，如果脚本失效，可能需要更新这些值
id_headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=789d74928d5e41438b72b7dc38fc87a2,sentry-sample_rate=0.1,sentry-transaction=%2Febk-room%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "789d74928d5e41438b72b7dc38fc87a2-9503a5d15466cf1c-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxI9ABzLxxIVn0bakNzA2GgsCYhl8dWX7o85pxEzK050SIi80Mx70ntTOUx7xCSmvtPvC/PnRLP0VB7nCini0Bfn3Xg9QR8hQrJinKeBBezaYleB200iYT+vQuzVLK3f5dLlutTm2BmqYY6ehiDQz/PnCDUQVI7Y7fPbEVP4CsjEVdcEVK2tCTKcXUpXtUMJV2wxlw7RaOPvVd7INBUsu1XXOC3miB5LLU3ZDe7PnX3gWJwfpFfPJ05Lpj8700G8auPEVBPERdh4VB1LCMnkNUbPOzSPu1NEW0YvAF/NhkpSxaxhgn+z7Xix/+cPIO84z2BhFC3VuGeUhM+8Bl3rHFIfxMK5t+tQWXKEx2UzspjPQY9dmeO5My7kEIoJH2tibNCdD3lV3JnJH2CwvJ20zK792sw3WUCM46MyNN51/TKoJXkskuKuv2p6K9j3W2KOhzcFxXSoZuKukqvn4AiNwYvNzrivW3Nz07eTBLx5QNKImbONTzKkt2shVsa7C9i7=="
}

room_headers = {

    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=4659fd630fa54e4caa5986aa846b0a40,sentry-sample_rate=0.1,sentry-transaction=%2Febk-room%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "4659fd630fa54e4caa5986aa846b0a40-8c27ac6ddf52d9a3-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxIt2ktctxIidjlsvkSkiGgsCYhl8dWX7o85pxEzK050SIi80Mx70ntTOXx7xtDGg0UUB7ynxSq+rpu0SKlNzcdZhrMGf796Y2jHF+KbbPOcahoSjhvuzVvTLHjCzoexRAzMSLo/WjNc51u0HQRIJzq/NCMiYnK0WWsyrrCsBApOzhYdPrForSCsBACgzFqaeRvnSkqPg3SwcQzmSVc0CynNudBTY3Cvc7T2Txstj+aIvhSyiF16ZEHnZbpekKV+thzU8Bqgf1yL0772StceCNSsZKi4bDPKsUkUxP+1gCrF3TG9mBN25Fop3fV9QtmdB9DZQ8mw3QV9OGmU9ft9WtmKOUK22SYKTr4zrLG9WV8UPxviancF1vt9hLMUEq6JBJrK4TGZgV2FZMG9fGnZUWl9VCj6TTyGOVO2SvnmsMeJsdpR+3pH7W6k7DTvpQcmF5lrpb6rG5lrtber73cHKXQg+Z+gslsJu5bi3TG9JCPi3TGGaj3m5wIx+Jax8Hi5j="

}


id_cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "1754eab5519eed1cbed1f94e8cdcc1e7",
    "lasttime": "1750382711556"
}
room_cookie = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "1754eab5519eed1cbed1f94e8cdcc1e7",
    "lasttime": "1750382711556"
}

# 需要查询的酒店名称列表
HOTEL_NAMES = [
    "清迈美平洲际酒店 - IHG 旗下酒店",
    "美憬阁普吉岛 V 别墅酒店",
    "苏梅岛金普顿基塔蕾度假酒店 - 洲际酒店集团旗下",
    "香港港丽酒店(康莱德)",
    "曼谷 W 酒店"
]

GET_ID_URL = "https://ebooking.elong.com/ebkorder/searchCondition/selectHotelsByName"
GET_ROOM_URL = "https://ebooking.elong.com/ebkroom/hotelManage/getMRoomTypes"
OUTPUT_FILE = "酒店房型信息.xlsx"

# --- 函数定义 ---

def get_hotel_id(hotel_name: str) -> str | None:
    """根据酒店名称，请求接口获取酒店ID。"""
    print(f"正在获取'{hotel_name}'的酒店ID...")
    data = {"hotelName": hotel_name, "provinceId": ""}
    data_str = json.dumps(data, separators=(',', ':'))
    try:
        response = requests.post(GET_ID_URL, headers=id_headers, cookies=id_cookies, data=data_str, timeout=10)
        response.raise_for_status()  # 如果请求失败则引发异常
        
        result = response.json()
        if result.get("hotelModels") and len(result["hotelModels"]) > 0:
            hotel_id = result["hotelModels"][0].get("shotelId")
            if hotel_id:
                print(f"  > 成功获取ID: {hotel_id}")
                return hotel_id
            else:
                print(f"  > 错误：在响应中未找到 'shotelId'。")
                return None
        else:
            print(f"  > 错误：未找到酒店'{hotel_name}'。")
            return None
    except requests.exceptions.RequestException as e:
        print(f"  > 请求酒店ID时出错: {e}")
        return None
    except json.JSONDecodeError:
        print(f"  > 解析酒店ID响应时出错，内容非JSON格式。响应: {response.text[:100]}")
        return None

def get_room_types(hotel_id: str) -> list:
    """根据酒店ID，请求接口获取房型列表。"""
    print(f"正在获取酒店ID'{hotel_id}'的房型信息...")
    data = {"shotelId": str(hotel_id)}  # 确保ID是字符串格式
    data_str = json.dumps(data, separators=(',', ':'))
    try:
        response = requests.post(GET_ROOM_URL, headers=room_headers, cookies=room_cookie, data=data_str, timeout=10)
        response.raise_for_status()

        result = response.json()
        room_list = result.get("mroomTypeList")
        if room_list is not None:  # 允许返回空列表
            print(f"  > 成功获取 {len(room_list)} 个房型。")
            return room_list
        else:
            print(f"  > 错误：在响应中未找到 'mroomTypeList'。")
            return []
    except requests.exceptions.RequestException as e:
        print(f"  > 请求房型信息时出错: {e}")
        return []
    except json.JSONDecodeError:
        print(f"  > 解析房型响应时出错，内容非JSON格式。响应: {response.text[:100]}")
        return []

# --- 主流程 ---

def main():
    """主函数，编排整个获取和保存流程。"""
    all_room_data = []
    
    for hotel_name in HOTEL_NAMES:
        hotel_id = get_hotel_id(hotel_name)
        
        if hotel_id:
            room_types = get_room_types(hotel_id)
            for room in room_types:
                all_room_data.append({
                    "酒店名称": hotel_name,
                    "酒店ID": hotel_id,
                    "房型ID": room.get("mroomTypeId"),
                    "房型名称": room.get("mroomTypeName")
                })
        
        print("-" * 30)
        time.sleep(1)  # 短暂停顿，避免请求过于频繁

    if not all_room_data:
        print("未能获取到任何房型数据，无法生成Excel文件。")
        return

    # 使用pandas创建DataFrame并保存到Excel
    print(f"正在将 {len(all_room_data)} 条房型数据写入到 {OUTPUT_FILE}...")
    df = pd.DataFrame(all_room_data)
    try:
        df.to_excel(OUTPUT_FILE, index=False, engine='openpyxl')
        print(f"成功！数据已保存到当前目录下的 '{OUTPUT_FILE}' 文件中。")
    except Exception as e:
        print(f"写入Excel文件时出错: {e}")

if __name__ == "__main__":
    try:
        import pandas
        import openpyxl
    except ImportError:
        print("错误：缺少必需的库 (pandas, openpyxl)。")
        print("请在命令行中运行: pip install pandas openpyxl")
    else:
        main() 
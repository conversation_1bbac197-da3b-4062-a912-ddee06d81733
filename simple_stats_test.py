#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的统计功能测试
"""

print("开始测试统计功能...")

# 模拟统计类
class ClickStatistics:
    def __init__(self):
        self.total_clicks = 0
        self.hotel_clicks = {}
        self.room_clicks = {}
        self.hotel_room_counts = {}
        
    def add_room_click(self, hotel_name: str, room_name: str, click_count: int = 1):
        # 更新总点击次数
        self.total_clicks += click_count
        
        # 更新酒店点击次数
        if hotel_name not in self.hotel_clicks:
            self.hotel_clicks[hotel_name] = 0
        self.hotel_clicks[hotel_name] += click_count
        
        # 更新房型点击次数
        if hotel_name not in self.room_clicks:
            self.room_clicks[hotel_name] = {}
        if room_name not in self.room_clicks[hotel_name]:
            self.room_clicks[hotel_name][room_name] = 0
        self.room_clicks[hotel_name][room_name] += click_count
        
    def add_hotel_room_count(self, hotel_name: str, room_count: int):
        self.hotel_room_counts[hotel_name] = room_count

# 测试统计功能
stats = ClickStatistics()

# 添加测试数据
print("添加测试数据...")
stats.add_hotel_room_count("新加坡费尔蒙酒店", 2)
stats.add_room_click("新加坡费尔蒙酒店", "豪华大床房", 25)
stats.add_room_click("新加坡费尔蒙酒店", "豪华港景房", 32)

stats.add_hotel_room_count("香港半岛酒店", 1)
stats.add_room_click("香港半岛酒店", "豪华客房", 28)

# 验证结果
print(f"总点击次数: {stats.total_clicks}")
print(f"酒店数量: {len(stats.hotel_clicks)}")

print("\n各酒店点击统计:")
for hotel_name, clicks in stats.hotel_clicks.items():
    room_count = stats.hotel_room_counts.get(hotel_name, 0)
    print(f"  {hotel_name}: {clicks} 次点击, {room_count} 个房型")
    
    if hotel_name in stats.room_clicks:
        for room_name, room_clicks in stats.room_clicks[hotel_name].items():
            print(f"    - {room_name}: {room_clicks} 次")

# 验证计算
expected_total = 25 + 32 + 28  # 85
if stats.total_clicks == expected_total:
    print(f"\n✓ 总点击次数正确: {stats.total_clicks}")
else:
    print(f"\n✗ 总点击次数错误: 期望 {expected_total}, 实际 {stats.total_clicks}")

print("\n✓ 统计功能测试完成！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量更新功能
验证按页面批量处理，避免重复翻页的逻辑
"""

import datetime
from tongcheng_price_maintain import PriceMaintainer

def test_batch_update_logic():
    """测试批量更新逻辑"""
    
    print("=== 批量更新逻辑测试 ===")
    
    # 创建测试实例
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    # 模拟30天的日期列表
    today = datetime.date.today()
    date_list = [(today + datetime.timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30)]
    
    print(f"测试日期范围: {date_list[0]} 到 {date_list[-1]}")
    print(f"总天数: {len(date_list)}")
    
    # 模拟需要更新的格子（分布在不同页面）
    updates_needed = [
        # 第1页的更新（日期索引 0-13）
        (date_list[2], 0, 1200, "价格差异超过5%"),   # 第3天
        (date_list[5], 1, None, "需设无效"),          # 第6天
        (date_list[10], 2, 1300, "需设有效价格"),     # 第11天
        
        # 第2页的更新（日期索引 14-27）
        (date_list[15], 0, 1400, "价格差异超过5%"),   # 第16天
        (date_list[20], 3, 1500, "需设有效价格"),     # 第21天
        
        # 第3页的更新（日期索引 28-29）
        (date_list[28], 1, None, "需设无效"),         # 第29天
    ]
    
    print(f"\n模拟需要更新的格子: {len(updates_needed)} 个")
    for date_str, row, price, reason in updates_needed:
        date_idx = date_list.index(date_str)
        page_idx = date_idx // 14
        print(f"  {date_str} (第{date_idx+1}天, 第{page_idx+1}页) 第{row+1}行: {reason}")
    
    # 测试分组逻辑
    print(f"\n🧪 测试分组逻辑...")
    page_updates = maintainer.group_updates_by_page(updates_needed, date_list)
    
    # 验证分组结果
    expected_pages = {0, 1, 2}  # 应该分布在3个页面
    actual_pages = set(page_updates.keys())
    
    print(f"\n✅ 分组验证:")
    print(f"  预期页面: {sorted(expected_pages)}")
    print(f"  实际页面: {sorted(actual_pages)}")
    print(f"  分组正确: {expected_pages == actual_pages}")
    
    # 验证每页的更新数量
    for page_idx in sorted(page_updates.keys()):
        updates = page_updates[page_idx]
        print(f"  第{page_idx+1}页: {len(updates)} 个更新")
        for date_str, row, price, reason, date_index in updates:
            print(f"    - {date_str} 第{row+1}行: {reason}")

def test_navigation_logic():
    """测试导航逻辑"""
    
    print("\n=== 导航逻辑测试 ===")
    
    # 测试不同的导航场景
    test_cases = [
        (0, 0, "同一页面"),
        (0, 1, "第1页到第2页"),
        (0, 2, "第1页到第3页"),
        (2, 0, "第3页到第1页"),
        (1, 2, "第2页到第3页"),
        (2, 1, "第3页到第2页"),
    ]
    
    print("导航测试用例:")
    for current, target, description in test_cases:
        if current == target:
            action = "无需翻页"
        elif target > current:
            action = f"向前翻 {target - current} 页"
        else:
            action = f"向后翻 {current - target} 页"
        
        print(f"  {description}: {action}")

def test_efficiency_comparison():
    """测试效率对比"""
    
    print("\n=== 效率对比测试 ===")
    
    # 模拟30天数据，随机分布需要更新的格子
    total_cells = 30 * 4  # 30天 × 4行
    
    # 场景1: 传统方式（每个格子都要翻页查找）
    updates_traditional = [
        "查找第1天 -> 翻页0次",
        "查找第5天 -> 翻页0次", 
        "查找第16天 -> 翻页1次 + 回到第1页",
        "查找第20天 -> 翻页1次 + 回到第1页",
        "查找第29天 -> 翻页2次 + 回到第1页",
    ]
    
    traditional_page_operations = 0 + 0 + 2 + 2 + 4  # 总翻页操作次数
    
    # 场景2: 批量方式（按页面分组处理）
    updates_batch = [
        "处理第1页: 2个更新 -> 翻页0次",
        "处理第2页: 2个更新 -> 翻页1次",
        "处理第3页: 1个更新 -> 翻页1次",
        "回到第1页 -> 翻页2次",
    ]
    
    batch_page_operations = 0 + 1 + 1 + 2  # 总翻页操作次数
    
    print("传统方式 (逐个查找):")
    for i, operation in enumerate(updates_traditional, 1):
        print(f"  {i}. {operation}")
    print(f"  总翻页操作: {traditional_page_operations} 次")
    
    print("\n批量方式 (按页面分组):")
    for i, operation in enumerate(updates_batch, 1):
        print(f"  {i}. {operation}")
    print(f"  总翻页操作: {batch_page_operations} 次")
    
    efficiency_improvement = ((traditional_page_operations - batch_page_operations) / traditional_page_operations * 100)
    print(f"\n📈 效率提升: {efficiency_improvement:.1f}% (减少了 {traditional_page_operations - batch_page_operations} 次翻页操作)")

def test_real_scenario():
    """测试真实场景"""
    
    print("\n=== 真实场景模拟 ===")
    
    # 模拟真实的价格维护场景
    today = datetime.date.today()
    date_list = [(today + datetime.timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30)]
    
    # 模拟实际可能的更新分布
    real_updates = []
    
    # 第1页：今天和明天需要调价
    real_updates.extend([
        (date_list[0], 0, 1200, "今日调价"),
        (date_list[1], 0, 1250, "明日调价"),
        (date_list[1], 1, 1300, "明日调价"),
    ])
    
    # 第2页：周末价格调整
    weekend_start = 14  # 假设第15天是周末开始
    real_updates.extend([
        (date_list[weekend_start], 0, None, "周末关房"),
        (date_list[weekend_start + 1], 0, None, "周末关房"),
        (date_list[weekend_start], 1, 1800, "周末涨价"),
        (date_list[weekend_start + 1], 1, 1800, "周末涨价"),
    ])
    
    # 第3页：月末促销
    month_end = 28
    real_updates.extend([
        (date_list[month_end], 2, 999, "月末促销"),
        (date_list[month_end + 1], 2, 999, "月末促销"),
    ])
    
    print(f"真实场景: {len(real_updates)} 个更新，分布在 {len(date_list)} 天中")
    
    # 分析更新分布
    page_distribution = {}
    for date_str, row, price, reason in real_updates:
        date_idx = date_list.index(date_str)
        page_idx = date_idx // 14
        if page_idx not in page_distribution:
            page_distribution[page_idx] = 0
        page_distribution[page_idx] += 1
    
    print("\n更新分布:")
    total_pages_needed = len(page_distribution)
    for page_idx in sorted(page_distribution.keys()):
        count = page_distribution[page_idx]
        print(f"  第{page_idx + 1}页: {count} 个更新")
    
    print(f"\n📊 效率分析:")
    print(f"  需要访问的页面: {total_pages_needed} 页")
    print(f"  传统方式翻页次数: {len(real_updates) * 2} 次 (每次更新都要来回翻页)")
    print(f"  批量方式翻页次数: {total_pages_needed + (total_pages_needed - 1)} 次 (访问每页 + 回到第1页)")
    
    traditional_ops = len(real_updates) * 2
    batch_ops = total_pages_needed + (total_pages_needed - 1)
    improvement = ((traditional_ops - batch_ops) / traditional_ops * 100)
    
    print(f"  效率提升: {improvement:.1f}%")

if __name__ == "__main__":
    test_batch_update_logic()
    test_navigation_logic()
    test_efficiency_comparison()
    test_real_scenario()

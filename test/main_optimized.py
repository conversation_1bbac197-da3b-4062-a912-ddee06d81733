#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的同程价格维护主程序
解决日期滚动问题，提高维护效率
"""

import datetime
import time
import sys
from tongcheng_price_maintain import PriceMaintainer, find_and_enter_hotel
from DrissionPage import ChromiumPage
from config import get_config

def main():
    """主程序入口"""
    
    # 加载配置
    config = get_config()
    hotel_config = config['hotel']
    room_config = config['room']
    maintenance_config = config['maintenance']
    log_config = config['log']
    
    if log_config['show_detailed_log']:
        print("=== 优化后的同程价格维护系统 ===")
        print(f"🏨 酒店: {hotel_config['hotel_name']}")
        print(f"🏠 房型: {room_config['tongcheng_room_name']}")
        print(f"📅 维护天数: {hotel_config['days']}")
        print(f"💱 汇率: {maintenance_config['currency_rate']}")
        print(f"📊 价格阈值: {maintenance_config['price_threshold']*100}%")
        print(f"⏰ 检查间隔: {maintenance_config['check_interval']}小时")
        print()
    
    # 初始化浏览器和维护器
    page = ChromiumPage()
    maintainer = PriceMaintainer(
        page=page,
        hotel_code=hotel_config['hotel_code'],
        hotel_name=hotel_config['hotel_name'],
        days=hotel_config['days'],
        tongcheng_hotel_room_name=room_config['tongcheng_room_name']
    )
    
    # 设置汇率
    maintainer.currency_rate = maintenance_config['currency_rate']
    
    try:
        # 自动进入酒店（如果需要）
        if input("是否需要自动搜索并进入酒店？(y/N): ").strip().lower() == 'y':
            print("🔍 正在搜索并进入酒店...")
            find_and_enter_hotel(page, hotel_config['hotel_name'])
            print("✅ 已进入酒店管理页面")
            time.sleep(3)
        
        # 持续监控模式
        run_continuous_monitoring(maintainer, config)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🔚 程序结束")

def run_continuous_monitoring(maintainer, config):
    """运行持续监控"""
    
    hotel_config = config['hotel']
    room_config = config['room']
    maintenance_config = config['maintenance']
    log_config = config['log']
    
    cycle_count = 0
    
    while True:
        cycle_count += 1
        
        if log_config['show_detailed_log']:
            print("=" * 80)
            print(f"🔄 第 {cycle_count} 轮维护 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
        
        try:
            # 步骤1: 获取价格数据
            if log_config['show_detailed_log']:
                print("\n📊 获取价格数据...")
            
            start_time = time.time()
            price_data = PriceMaintainer.get_first_room_price_data(
                hotel_config['hotel_code'], 
                hotel_config['days'], 
                room_config['badazhou_room_name']
            )
            price_data = PriceMaintainer.fill_missing_dates(price_data, hotel_config['days'])
            data_time = time.time() - start_time
            
            if log_config['show_detailed_log']:
                valid_prices = [p for p in price_data.values() if p is not None]
                print(f"✅ 获取到{len(price_data)}天数据，耗时: {data_time:.1f}秒")
                print(f"   📈 有效价格: {len(valid_prices)}天")
                print(f"   🚫 无效/关房: {len(price_data) - len(valid_prices)}天")
                if valid_prices:
                    print(f"   💰 价格范围: {min(valid_prices)} - {max(valid_prices)} 元")
            
            # 步骤2: 执行智能维护
            if log_config['show_detailed_log']:
                print("\n🔧 执行智能价格维护...")
            
            maintenance_start = time.time()
            changes_made = maintainer.smart_price_maintenance(
                price_data, 
                threshold=maintenance_config['price_threshold']
            )
            maintenance_time = time.time() - maintenance_start
            
            # 步骤3: 统计和报告
            if log_config['show_efficiency_stats']:
                total_cells = hotel_config['days'] * 4
                efficiency = ((total_cells - changes_made) / total_cells * 100) if total_cells > 0 else 0
                
                print(f"\n📈 维护结果:")
                print(f"   🎯 总格子数: {total_cells}")
                print(f"   ✏️  更新格子数: {changes_made}")
                print(f"   ⏭️  跳过格子数: {total_cells - changes_made}")
                print(f"   🚀 维护效率: {efficiency:.1f}% 无需更新")
                print(f"   ⏱️  维护耗时: {maintenance_time:.1f}秒")
            
            if changes_made == 0:
                print("✅ 所有价格已是最新，无需更新！")
            else:
                print(f"✅ 本轮成功更新了 {changes_made} 个格子")
            
            # 步骤4: 等待下一轮
            if log_config['show_detailed_log']:
                next_check = datetime.datetime.now() + datetime.timedelta(hours=maintenance_config['check_interval'])
                print(f"\n⏰ 下次检查时间: {next_check.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"💤 等待 {maintenance_config['check_interval']} 小时...")
            
            # 等待指定时间后再次检查
            time.sleep(maintenance_config['check_interval'] * 60 * 60)
            
        except Exception as e:
            print(f"\n❌ 第 {cycle_count} 轮维护出错: {e}")
            print("⏳ 等待5分钟后重试...")
            time.sleep(5 * 60)

def run_single_check():
    """运行单次检查（用于测试）"""
    
    config = get_config()
    hotel_config = config['hotel']
    room_config = config['room']
    maintenance_config = config['maintenance']
    
    print("=== 单次价格检查模式 ===")
    
    # 初始化
    page = ChromiumPage()
    maintainer = PriceMaintainer(
        page=page,
        hotel_code=hotel_config['hotel_code'],
        hotel_name=hotel_config['hotel_name'],
        days=hotel_config['days'],
        tongcheng_hotel_room_name=room_config['tongcheng_room_name']
    )
    
    try:
        # 获取价格数据
        print("📊 获取价格数据...")
        price_data = PriceMaintainer.get_first_room_price_data(
            hotel_config['hotel_code'], 
            hotel_config['days'], 
            room_config['badazhou_room_name']
        )
        price_data = PriceMaintainer.fill_missing_dates(price_data, hotel_config['days'])
        
        # 执行维护
        print("🔧 执行价格维护...")
        changes_made = maintainer.smart_price_maintenance(
            price_data, 
            threshold=maintenance_config['price_threshold']
        )
        
        print(f"\n✅ 单次检查完成，更新了 {changes_made} 个格子")
        
    except Exception as e:
        print(f"❌ 单次检查出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "single":
            # 单次检查模式
            run_single_check()
        elif sys.argv[1] == "config":
            # 显示配置
            from config import print_config
            print_config()
        else:
            print("用法:")
            print("  python main_optimized.py        # 持续监控模式")
            print("  python main_optimized.py single # 单次检查模式")
            print("  python main_optimized.py config # 显示配置")
    else:
        # 默认持续监控模式
        main()

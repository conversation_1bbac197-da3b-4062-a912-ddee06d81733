#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零点后时间窗口处理示例
展示如何处理过零点后页面还未更新的情况
"""

import datetime
import time
from tongcheng_price_maintain import PriceMaintainer
from DrissionPage import ChromiumPage

def main():
    """主程序：展示零点后时间窗口处理"""
    
    # 配置参数
    hotel_code = "ucjawg449284"
    hotel_name = "香港丽思卡尔顿酒店"
    badazhou_hotel_room_name = '豪华客房(双床)'
    tongcheng_hotel_room_name = '豪华双床房'
    days = 30
    
    print("=== 零点后时间窗口处理演示 ===")
    print(f"🏨 酒店: {hotel_name}")
    print(f"🏠 房型: {tongcheng_hotel_room_name}")
    print(f"📅 维护天数: {days}")
    print(f"🕛 新特性: 智能处理零点后的时间窗口问题")
    print()
    
    # 检查当前时间
    current_time = datetime.datetime.now()
    print(f"⏰ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 判断是否可能处于时间窗口
    if 0 <= current_time.hour <= 7:
        print(f"⚠️  当前时间可能处于零点后时间窗口（00:00-07:00）")
        print(f"   系统将自动检测并处理页面日期显示异常")
    else:
        print(f"✅ 当前时间为正常时段，页面日期显示应该正常")
    
    print()
    
    # 初始化
    page = ChromiumPage()
    maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)
    
    try:
        # 演示时间窗口检测和处理
        demo_midnight_handling()
        
        # 执行实际维护
        if input("是否执行实际的价格维护（包含时间窗口处理）？(y/N): ").strip().lower() == 'y':
            run_maintenance_with_midnight_handling(maintainer, hotel_code, days, badazhou_hotel_room_name)
        else:
            print("跳过实际维护，演示完成")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

def demo_midnight_handling():
    """演示零点后时间窗口处理逻辑"""
    
    print("🕛 零点后时间窗口处理演示")
    print("=" * 50)
    
    # 模拟不同时间的页面状态
    scenarios = [
        {
            "time": "2025-07-07 14:30",
            "description": "正常时段 - 下午2点30分",
            "page_display": ["今日", "8", "9", "10", "11", "12", "13"],
            "expected_behavior": "正常解析，无需特殊处理"
        },
        {
            "time": "2025-07-07 01:30",
            "description": "零点后时间窗口 - 凌晨1点30分",
            "page_display": ["6", "7", "8", "9", "10", "11", "12"],
            "expected_behavior": "检测异常，修正第一列为今天"
        },
        {
            "time": "2025-08-01 02:00",
            "description": "跨月时间窗口 - 8月1号凌晨2点",
            "page_display": ["31", "1", "2", "3", "4", "5", "6"],
            "expected_behavior": "检测跨月异常，修正第一列为今天"
        },
        {
            "time": "2025-07-07 07:30",
            "description": "早晨时段 - 早上7点30分",
            "page_display": ["今日", "8", "9", "10", "11", "12", "13"],
            "expected_behavior": "页面已更新，正常解析"
        }
    ]
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    for scenario in scenarios:
        print(f"\n📋 场景: {scenario['description']}")
        print(f"   时间: {scenario['time']}")
        print(f"   页面显示: {scenario['page_display']}")
        print(f"   预期行为: {scenario['expected_behavior']}")
        
        # 解析时间
        time_obj = datetime.datetime.strptime(scenario['time'], '%Y-%m-%d %H:%M')
        today = time_obj.date()
        
        # 模拟日期解析
        print(f"   解析结果:")
        for i, date_text in enumerate(scenario['page_display'][:5]):  # 只显示前5个
            col_idx = i + 2
            
            # 临时修改today用于测试
            original_today = datetime.date.today
            datetime.date.today = lambda: today
            
            try:
                parsed = maintainer.parse_page_date(date_text, col_idx)
                print(f"     列{col_idx}: '{date_text}' -> '{parsed}'")
            except Exception as e:
                print(f"     列{col_idx}: '{date_text}' -> 错误: {e}")
            finally:
                datetime.date.today = original_today
        
        # 验证第一列是否正确
        first_col = scenario['page_display'][0]
        if first_col == "今日":
            print(f"   ✅ 第一列显示正常")
        elif first_col == str(today.day - 1) or (today.day == 1 and int(first_col) > 28):
            print(f"   ⚠️  检测到时间窗口，需要修正第一列")
        else:
            print(f"   ✅ 第一列显示正常（未来日期）")

def run_maintenance_with_midnight_handling(maintainer, hotel_code, days, badazhou_hotel_room_name):
    """执行带时间窗口处理的价格维护"""
    
    print("\n🔧 开始带时间窗口处理的价格维护")
    print("=" * 50)
    
    try:
        # 步骤1: 检测当前时间状态
        current_time = datetime.datetime.now()
        print(f"📅 步骤1: 检测当前时间状态")
        print(f"   当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 0 <= current_time.hour <= 7:
            print(f"   ⚠️  当前处于可能的时间窗口期（00:00-07:00）")
            print(f"   系统将启用智能时间窗口检测和修正")
        else:
            print(f"   ✅ 当前为正常时段")
        
        # 步骤2: 获取价格数据
        print(f"\n📊 步骤2: 获取价格数据")
        start_time = time.time()
        
        price_data = PriceMaintainer.get_first_room_price_data(hotel_code, days, badazhou_hotel_room_name)
        price_data = PriceMaintainer.fill_missing_dates(price_data, days)
        
        data_time = time.time() - start_time
        print(f"   ✅ 获取到{len(price_data)}天价格数据，耗时: {data_time:.1f}秒")
        
        # 步骤3: 测试页面日期解析（带时间窗口处理）
        print(f"\n🕛 步骤3: 测试页面日期解析（带时间窗口处理）")
        try:
            # 使用新的上下文日期解析
            page_dates = maintainer.get_date_sequence_with_context()
            print(f"   ✅ 成功解析页面日期: {len(page_dates)}个")
            print(f"   首个日期: {page_dates[0] if page_dates else 'None'}")
            print(f"   最后日期: {page_dates[-1] if page_dates else 'None'}")
            
            # 验证今天的日期是否正确
            today_str = datetime.date.today().strftime('%Y-%m-%d')
            if page_dates and page_dates[0] == today_str:
                print(f"   ✅ 第一列日期正确匹配今天: {today_str}")
            elif page_dates:
                print(f"   ⚠️  第一列日期与今天不匹配: {page_dates[0]} vs {today_str}")
            
        except Exception as e:
            print(f"   ❌ 页面日期解析失败: {e}")
            return
        
        # 步骤4: 执行智能维护（自动处理时间窗口）
        print(f"\n🚀 步骤4: 执行智能价格维护（自动处理时间窗口）")
        maintenance_start = time.time()
        
        changes_made = maintainer.smart_price_maintenance(price_data)
        
        maintenance_time = time.time() - maintenance_start
        
        # 步骤5: 结果统计
        total_cells = days * 4
        efficiency = ((total_cells - changes_made) / total_cells * 100) if total_cells > 0 else 0
        
        print(f"\n📈 维护结果（含时间窗口处理）:")
        print(f"   🎯 总格子数: {total_cells}")
        print(f"   ✏️  更新格子数: {changes_made}")
        print(f"   ⏭️  跳过格子数: {total_cells - changes_made}")
        print(f"   🚀 维护效率: {efficiency:.1f}% 无需更新")
        print(f"   ⏱️  维护耗时: {maintenance_time:.1f}秒")
        print(f"   🕛 时间窗口处理: 自动检测和修正")
        
        if changes_made == 0:
            print("✅ 所有价格已是最新，无需更新！")
        else:
            print(f"✅ 成功更新了 {changes_made} 个格子（已处理时间窗口问题）")
            
    except Exception as e:
        print(f"❌ 维护过程出错: {e}")
        import traceback
        traceback.print_exc()

def check_current_time_window():
    """检查当前是否处于时间窗口"""
    
    print("\n🕛 当前时间窗口状态检查")
    print("=" * 30)
    
    current_time = datetime.datetime.now()
    hour = current_time.hour
    
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前小时: {hour}")
    
    if 0 <= hour <= 7:
        print(f"⚠️  当前可能处于零点后时间窗口")
        print(f"   - 页面可能还未更新到今天的日期")
        print(f"   - 第一列可能显示昨天的数字而不是'今日'")
        print(f"   - 系统将自动检测并修正这种情况")
        
        if hour <= 2:
            print(f"   - 时间窗口概率: 高（刚过零点）")
        elif hour <= 5:
            print(f"   - 时间窗口概率: 中等")
        else:
            print(f"   - 时间窗口概率: 低（接近更新时间）")
    else:
        print(f"✅ 当前为正常时段，页面日期显示应该正常")
    
    return 0 <= hour <= 7

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 完整演示（包含实际维护）")
    print("2. 仅时间窗口处理演示")
    print("3. 检查当前时间窗口状态")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        demo_midnight_handling()
    elif choice == "3":
        check_current_time_window()
    else:
        print("无效选择，运行默认演示")
        demo_midnight_handling()

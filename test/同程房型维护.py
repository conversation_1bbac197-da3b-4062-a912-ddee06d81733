from DrissionPage import ChromiumPage
import time
from DrissionPage.errors import NoRectError
page = ChromiumPage()

hotel_name = '新加坡费尔蒙酒店'
import datetime




# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[2]/div/span[1]/span
# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[3]/div/span[1]/span
# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[4]/div/span[1]/span
# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[15]/div/span[1]/span


# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[2]/div[3]
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[3]/div[3]

# 6号
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[6]/div[3]/span/span/div
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[7]/div[3]

# 8号
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[10]/div[3]


'''
2号对应的表头日期是 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[2]/div
th[2] 表示第一列
2号对应要填入的价格是  
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[2]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[3]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[4]/div[3]
 其中 tr[2] 表示第一行 td[2] 表示 第一列 td[3] 表示第二列  td[4] 表示第三列
 
 3号对应的表头 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[3]/div
 3号对应要填入的价格是  
  //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[3]/td[3]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[3]/td[4]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[3]/td[5]/div[3]
 
 4号//*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[4]/div
   //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[4]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[5]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[6]/div[3]
 
 以此类推到 13号 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[13]/div
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[5]/td[13]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[5]/td[14]/div[3]/span/span/div
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[5]/td[15]/div[3]
 


 但是一页只有 4个行 即 4个 tr 15个td 所以16号的时候就要点击后两周会进行重置，重置又分情况
 第一种是行不够了列还剩，比如14号 15号 ，一种是列不够，行还剩
 
  14 号
 表头 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[14]/div
 价格 
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[2]/td[14]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[2]/td[15]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[2]/td[2]/div[3] #需要先点击后两周
 
 15号 表头 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[15]/div
  //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[3]/td[2]/div[3] 
  //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[4]/tr[3]/td[3]/div[3]
 
 16号
   //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[4]/td[2]/div[3]
  //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[4]/td[3]/div[3]
  //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[4]/td[4]/div[3]
 
 
 17号 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[3]/div
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[5]/td[3]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[5]/td[4]/div[3]
 //*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[5]/td[5]/div[3]
 
 
 18号 表头 //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[4]/div
 表价格
//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[2]/td[4]
//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[2]/td[5]
//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[2]/td[6]
  
  
'''


# def get_price_xpath(date_idx, page_size=15, row_size=4):
#     tr = 2 + (date_idx % row_size)
#     base = ((date_idx // row_size) * row_size + (date_idx % row_size))
#     td1 = 2 + (base % page_size)
#     td2 = td1 + 1 if td1 + 1 <= 15 else 2
#     td3 = td2 + 1 if td2 + 1 <= 15 else 2
#     th = td1
#     date_xpath = f'//*[@id=\"product-app\"]/div/div[3]/div[4]/table/thead/tr/th[{th}]/div'
#     price_xpath_list = [
#         f'//*[@id=\"product-app\"]/div/div[3]/div[4]/table/tbody/tr[{tr}]/td[{td1}]/div[3]',
#         f'//*[@id=\"product-app\"]/div/div[3]/div[4]/table/tbody/tr[{tr}]/td[{td2}]/div[3]',
#         f'//*[@id=\"product-app\"]/div/div[3]/div[4]/table/tbody/tr[{tr}]/td[{td3}]/div[3]',
#     ]
#     return date_xpath, price_xpath_list
#
#
#
#
# date = 17
# date_xpath, result = get_price_xpath(date)
# print( date_xpath, result)

# room_name1 = '费尔蒙客房'
# room_name2 = '特色金尊套房，配备 1 张特大床和 2 个阳台，尊享费尔蒙金尊休闲区礼遇'

# import random  # 新增：导入random用于生成随机价格
#
# # 生成room_name1和room_name2从今天开始到未来30天的房价数据，每天价格用随机数做区分
# def generate_room_price_data_random(room_name, start_date, days, base_price=1000, delta=30):
#     """
#     生成指定房型从start_date起连续days天的房价数据，每天价格用随机数做区分
#     :param room_name: 房型名称
#     :param start_date: 开始日期(datetime.date)
#     :param days: 天数
#     :param base_price: 基础价格
#     :param delta: 随机浮动范围
#     :return: {date_str: price}
#     """
#     price_data = {}
#     for i in range(days):
#         date_str = (start_date + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
#         # 新增：每天价格用随机数做区分
#         price_data[date_str] = base_price + random.randint(-delta, delta)
#     return price_data
#
# today = datetime.date.today()
# days = 30
#
# room1_price_data = generate_room_price_data_random(room_name1, today, days)
# room2_price_data = generate_room_price_data_random(room_name2, today, days, base_price=1200)
#
# # 新增：打印样例数据，便于查看
# print(f"{room_name1} 房价数据样例：", list(room1_price_data.items())[:3])
# print(f"{room_name2} 房价数据样例：", list(room2_price_data.items())[:3])
#
# # 获取总房型名称
# room_table = page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/table')
# tbodys = room_table.eles('x:/tbody')
# for tbody in tbodys:
#     room_name = tbody.ele('x:/tr[1]/td[1]/div[1]/span').text
#     print(room_name)
    
    

# '费尔蒙客房'
# # 下面是房型的第一列价格计划id
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[2]/td[2]/div[3]'
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[3]/td[2]/div[3]'
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[1]/tr[4]/td[2]/div[3]'

# '特色金尊套房，配备 1 张特大床和 2 个阳台，尊享费尔蒙金尊休闲区礼遇'
# # 下面是房型的第一列价格计划id
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[2]/tr[2]/td[2]/div[3]'
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[2]/tr[3]/td[2]/div[3]'
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody[2]/tr[4]/td[2]/div[3]'




# 点击批量维护房态
# page.ele('x://*[@id="room-app"]/div/div[3]/div/div/div/div/div[1]/div/button').click()


# # 房态维护选择房型
# divs = page.eles('x://*[@id="room-app"]/div/div[2]/div[2]/div[1]/div[1]/div[3]/div[1]/div')
# for div in divs:
#     div.ele('x:/label/span[1]').click()
#     print(div.ele('x:/label/span[2]').text) # 获取房型名称


# 房态维护点击日期
# open_room_date = '2025-12-20'
# date_input = page.ele('x://*[@id="room-app"]/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div[3]/input')
# date_input.click()
# page.ele('x:el-input__icon').click() # 删掉原来日期
# date_input.input(open_room_date)


# # 保留房售卖时间
# page.ele('x://*[@id="room-app"]/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[5]/div[2]/div/div/div[1]/label/span[2]').click()
#
# # 保存修改
# page.ele('x://*[@id="room-app"]/div/div[2]/div[2]/div[2]/button[3]').click()

# 房型数量
# page.ele('x://*[@id="room-app"]/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[3]/div[2]/div[1]/div').click()
# page.ele('x:/html/body/div[43]/div[1]/div[1]/ul/li[3]').click()  # 点击统一设置

# room_count = '2'
# room_input = page.ele('x://*[@id="room-app"]/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[3]/div[2]/div[2]/div/input')
# room_input.click()
# room_input.clear()
# room_input.input(room_count)

# def click_next_two_weeks(page):
#     """
#     点击'后两周'按钮
#     """
#     next_btn = page.ele('text:后两周')
#     if next_btn:
#         next_btn.click()
#         time.sleep(2)
#         return True
#     else:
#         print("未找到'后两周'按钮，无法继续维护后续日期！")
#         return False
#
# click_next_two_weeks(page)

# # 点击酒店名称
# page.ele('x://*[@id="common_app"]/div/div[2]/div/div[2]/span[1]/span/div/span').click()
#
# # 点击酒店输入框名称
# input_line = page.ele('x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/div[5]/div/input')
#
# input_line.click()
# time.sleep(1)
# input_line.input(hotel_name)
#
# time.sleep(1)
#
# # 点击酒店名称备选项
# page.ele('.el-tooltip option-text item').click()
# time.sleep(1)
# #  点击查询
# search_button = page.ele('x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/button')
# search_button.click()
#
# time.sleep(1)
# # 点击查询到的酒店名称
# page.ele('.w-1/3 pt-1.0 pb-1.0 text-14 cursor-pointer').click()
# time.sleep(1) #等待跳转
#
#


# 点击对应日期的价格维护
# page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[2]').click()
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[3]/td[3]'
# '//*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[4]'



# page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[6]').click()

# # 获取房型名称
# room_name = page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[1]/div/div[3]/div[@class="room-type"]').text
# print(f"当前维护房型：{room_name}")
#
# print(room_name)
# 点击修改价格
# page.ele('x://button[@class="el-button mb-[10px] w-[142px] el-button--primary el-button--medium"]').click()

# page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[1]/form/div[6]/div/div/div/div[1]/div[1]/div/div/div[1]/input').clear()

# # 输入价格维护
# page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[1]/form/div[6]/div/div/div/div[1]/div[1]/div/div/div[1]/input').input('1500')

# # 点击保存
# page.ele('x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[2]/button[2]').click()
# time.sleep(1)
# # 点击关闭
# page.ele('.el-button el-button--default el-button--small el-button--primary ').click()

# from DrissionPage.common import Keys
# page.ele('.').input(Keys.ENTER)

# 点击后两周的价格维护

# page.ele('text:后两周').click()
# page.ele('text:前两周').click()




# 点击设置价格无效
# page.ele('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain').click()

# 点击设置价格有效
# page.ele('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain').click()


# # 获取格子的价格
# price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[5]/div[3]/span/span/div/p[2]'
# price_text = page.ele(price_text_xpath).text
# print(price_text)


# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[2]/div/span[1]/span
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[1]/td[2]/div/div


# //*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[3]/div/span[1]/span   #今日

# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[1]/td[3]/div/div # 无房



#按行获取价格 #共4行
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[3]/div[3]/span/span/div/p[2] # 6666
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[3]/td[3]/div[3]/p #无效
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[4]/td[3]/div[3]#无效
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[5]/td[3]/div[3]#无效


# 按行获取价格
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[4]/div[3]/span/span/div/p[2] 今日日期价格
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[5]/div[3]/span/span/div/p[2] 今日日期+1 价格
# //*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[2]/td[6]/div[3]/span/span/div/p[2] 今日日期+2 价格

# start_index = None

# for i in range(2,4):
#     text = page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
#     if text == '今日':
#         start_index = i
#         print('开始元素为：',i)
#
#
# for i in range(2,6):
#     # 尝试获取价格，先尝试有价格的xpath
#     try:
#         price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{i}]/td[3]/div[3]/span/span/div/p[2]'
#         price_text = page.ele(price_text_xpath, timeout=0.5).text
#         print(f"第{i}行有价格: {price_text}")
#     except:
#         # 如果失败，尝试无效价格的xpath
#         try:
#             price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{i}]/td[3]/div[3]/p'
#             price_text = page.ele(price_text_xpath, timeout=0.5).text
#             print(f"第{i}行无效价格: {price_text}")
#         except:
#             # 如果都失败，尝试基础xpath
#             try:
#                 price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{i}]/td[3]/div[3]'
#                 price_text = page.ele(price_text_xpath, timeout=0.5).text
#                 print(f"第{i}行基础获取: {price_text}")
#             except:
#                 print(f"第{i}行无法获取价格信息")

room_name = '豪华大床房（港景，带阳台）'
# ================= 新增：选不中房型时自动刷新重试 =================
from DrissionPage.errors import NoRectError  # 确保已导入异常
max_retry = 3  # 最多重试次数
for retry in range(1, max_retry + 1):
    room_type_sele = 'x://*[@id="product-app"]/div/div[3]/div[1]/form/div[1]/div/div/div[1]'
    page.ele(room_type_sele).click()
    time.sleep(0.5)  # 新增：等待下拉菜单展开
    li_elements = page.eles("x://body/div[@class='el-select-dropdown el-popper is-multiple']/div[@class='el-scrollbar']/div[@class='el-select-dropdown__wrap el-scrollbar__wrap']/ul[@class='el-scrollbar__view el-select-dropdown__list']/li")
    found = False
    for i, li_ele in enumerate(li_elements):
        room_name_option = li_ele.ele('x:/div/div').text
        # print(f"选项{i+1}: {room_name_option}")
        if room_name == room_name_option:
            try:
                li_ele.click()
                page.ele("x://div[@class='additionBtns flex items-center justify-center']/div[2]").click()  # 点击确定
                found = True
                break
            except NoRectError:
                # print(f"选项{i+1}: {room_name_option} 无法点击（元素无位置和大小），跳过")
                continue
    if found:
        break
    else:
        if retry < max_retry:
            print(f"未找到房型，刷新页面重试，第{retry}次")
            page.refresh()
            time.sleep(2)  # 等待页面加载
        else:
            print("多次刷新后仍未选中目标房型")
# ================= 新增功能结束 =================

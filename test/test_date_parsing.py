#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期解析功能
验证简化日期格式（今日 7 8 9 10 11）的解析
"""

import datetime
from tongcheng_price_maintain import PriceMaintainer

def test_date_parsing():
    """测试日期解析功能"""
    
    print("=== 日期解析功能测试 ===")
    
    # 创建一个测试实例（不需要真实的page对象）
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    # 测试用例
    test_cases = [
        "今日",
        "7",
        "8", 
        "9",
        "10",
        "11",
        "15",
        "20",
        "25",
        "30",
        "31",  # 可能跨月的情况
        "1",   # 可能是下个月的1号
        "invalid",  # 无效输入
        "",    # 空字符串
    ]
    
    today = datetime.date.today()
    print(f"今天是: {today.strftime('%Y-%m-%d')} ({today.day}号)")
    print(f"当前月份: {today.month}月")
    print()
    
    print("测试结果:")
    print("-" * 50)
    
    for i, test_input in enumerate(test_cases, 1):
        try:
            result = maintainer.parse_page_date(test_input)
            print(f"{i:2d}. 输入: '{test_input:8s}' -> 输出: '{result}'")
            
            # 验证结果格式
            if result != test_input:  # 如果有转换
                try:
                    parsed_date = datetime.datetime.strptime(result, '%Y-%m-%d').date()
                    days_diff = (parsed_date - today).days
                    print(f"     解析为: {parsed_date.strftime('%Y年%m月%d日')} (距今{days_diff:+d}天)")
                except ValueError:
                    print(f"     警告: 输出格式不是标准日期格式")
            print()
        except Exception as e:
            print(f"{i:2d}. 输入: '{test_input:8s}' -> 错误: {e}")
            print()

def test_month_boundary():
    """测试月份边界情况"""
    
    print("=== 月份边界测试 ===")
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    today = datetime.date.today()
    
    print(f"当前日期: {today}")
    print(f"当前月份有 {get_days_in_month(today.year, today.month)} 天")
    
    # 测试当前月份的最后几天
    current_month_days = get_days_in_month(today.year, today.month)
    test_days = [current_month_days - 1, current_month_days, current_month_days + 1]
    
    print("\n测试月份边界:")
    for day in test_days:
        if day > 0:
            try:
                result = maintainer.parse_page_date(str(day))
                parsed_date = datetime.datetime.strptime(result, '%Y-%m-%d').date()
                print(f"  {day:2d}号 -> {parsed_date} ({parsed_date.strftime('%m月%d日')})")
            except Exception as e:
                print(f"  {day:2d}号 -> 错误: {e}")

def get_days_in_month(year, month):
    """获取指定年月的天数"""
    if month == 12:
        next_month = datetime.date(year + 1, 1, 1)
    else:
        next_month = datetime.date(year, month + 1, 1)
    last_day = next_month - datetime.timedelta(days=1)
    return last_day.day

def test_real_scenario():
    """测试真实场景"""
    
    print("=== 真实场景模拟 ===")
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    # 模拟页面显示的日期序列
    page_dates = ["今日", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20"]
    
    print("模拟页面日期序列:")
    print("原始:", " ".join(page_dates))
    
    parsed_dates = []
    for date_text in page_dates:
        parsed = maintainer.parse_page_date(date_text)
        parsed_dates.append(parsed)
    
    print("解析后:")
    for i, (original, parsed) in enumerate(zip(page_dates, parsed_dates)):
        print(f"  {i+2:2d}列: {original:4s} -> {parsed}")
    
    # 检查日期连续性
    print("\n连续性检查:")
    for i in range(1, len(parsed_dates)):
        try:
            date1 = datetime.datetime.strptime(parsed_dates[i-1], '%Y-%m-%d').date()
            date2 = datetime.datetime.strptime(parsed_dates[i], '%Y-%m-%d').date()
            diff = (date2 - date1).days
            if diff == 1:
                print(f"  ✅ {parsed_dates[i-1]} -> {parsed_dates[i]} (连续)")
            else:
                print(f"  ⚠️  {parsed_dates[i-1]} -> {parsed_dates[i]} (间隔{diff}天)")
        except ValueError:
            print(f"  ❌ 日期格式错误: {parsed_dates[i-1]} -> {parsed_dates[i]}")

if __name__ == "__main__":
    test_date_parsing()
    print()
    test_month_boundary()
    print()
    test_real_scenario()

"""
多房型处理测试脚本
验证修复后的房型处理逻辑和多标签页功能
"""

import time
import unittest
from unittest.mock import Mock, patch
from tongcheng.pusher.tongcheng.tongcheng_pusher import TongchengPusher
from tongcheng.pusher.tongcheng.multi_tab_processor import MultiTabTongchengPusher
from tongcheng.config.multi_tab_config import MultiTabConfig


class TestMultiRoomProcessing(unittest.TestCase):
    """多房型处理测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.sample_hotel_data = {
            "同程酒店名称": "测试酒店",
            "房型数据": [
                {
                    "同程房型名称": "豪华大床房",
                    "价格数据": [{"2025-07-07": "1200.00"}]
                },
                {
                    "同程房型名称": "豪华双床房", 
                    "价格数据": [{"2025-07-07": "1100.00"}]
                },
                {
                    "同程房型名称": "行政套房",
                    "价格数据": [{"2025-07-07": "2000.00"}]
                }
            ]
        }
    
    def test_single_tab_room_processing_logic(self):
        """测试单标签页房型处理逻辑"""
        print("\n🧪 测试单标签页房型处理逻辑...")
        
        # 模拟推送器
        with patch('tongcheng.pusher.tongcheng.tongcheng_pusher.ChromiumPage'):
            pusher = TongchengPusher()
            
            # 模拟必要的组件
            pusher.hotel_operator = Mock()
            pusher.hotel_operator.find_and_enter_hotel.return_value = True
            
            pusher.price_operator = Mock()
            pusher.price_operator.smart_price_maintenance.return_value = True
            
            pusher.page = Mock()
            pusher.page.ele.return_value = Mock()
            
            # 测试房型处理
            result = pusher._process_single_room(
                "测试酒店", 
                "豪华大床房", 
                {"2025-07-07": "1200.00"}, 
                0
            )
            
            self.assertTrue(result, "单个房型处理应该成功")
            print("✅ 单标签页房型处理逻辑测试通过")
    
    def test_multi_room_state_management(self):
        """测试多房型状态管理"""
        print("\n🧪 测试多房型状态管理...")
        
        with patch('tongcheng.pusher.tongcheng.tongcheng_pusher.ChromiumPage'):
            pusher = TongchengPusher()
            
            # 模拟组件
            pusher.hotel_operator = Mock()
            pusher.hotel_operator.find_and_enter_hotel.return_value = True
            
            pusher.price_operator = Mock()
            pusher.price_operator.smart_price_maintenance.return_value = True
            
            pusher.page = Mock()
            pusher.page.ele.return_value = Mock()
            
            # 模拟推送价格方法
            with patch.object(pusher, '_process_single_room') as mock_process:
                mock_process.return_value = True
                
                with patch.object(pusher, '_click_price_maintenance_button') as mock_click:
                    mock_click.return_value = True
                    
                    with patch.object(pusher, '_reset_room_state') as mock_reset:
                        # 测试多房型处理
                        result = pusher.push_price(self.sample_hotel_data)
                        
                        # 验证每个房型都被处理
                        self.assertEqual(mock_process.call_count, 3, "应该处理3个房型")
                        
                        # 验证状态重置被调用（除了最后一个房型）
                        self.assertEqual(mock_reset.call_count, 2, "应该重置2次状态")
                        
                        self.assertTrue(result, "多房型处理应该成功")
            
            print("✅ 多房型状态管理测试通过")
    
    def test_multi_tab_config_validation(self):
        """测试多标签页配置验证"""
        print("\n🧪 测试多标签页配置验证...")
        
        # 测试有效配置
        valid_config = MultiTabConfig(max_tabs=3, room_interval=1.0)
        self.assertTrue(valid_config.validate(), "有效配置应该通过验证")
        
        # 测试无效配置
        invalid_config = MultiTabConfig(max_tabs=0, room_interval=-1.0)
        self.assertFalse(invalid_config.validate(), "无效配置应该验证失败")
        
        print("✅ 多标签页配置验证测试通过")
    
    def test_performance_estimation(self):
        """测试性能估算"""
        print("\n🧪 测试性能估算...")
        
        room_count = 4
        single_room_time = 30
        
        # 单标签页时间
        single_tab_time = room_count * single_room_time
        
        # 多标签页时间 (3个标签页)
        config = MultiTabConfig(max_tabs=3)
        batches = (room_count + config.max_tabs - 1) // config.max_tabs
        multi_tab_time = batches * single_room_time
        
        speedup = single_tab_time / multi_tab_time
        
        print(f"   单标签页时间: {single_tab_time}秒")
        print(f"   多标签页时间: {multi_tab_time}秒")
        print(f"   理论加速比: {speedup:.1f}x")
        
        self.assertGreater(speedup, 1.0, "多标签页应该比单标签页更快")
        self.assertAlmostEqual(speedup, 2.0, delta=0.1, msg="3个标签页处理4个房型的加速比应该约为2.0")
        
        print("✅ 性能估算测试通过")


def run_integration_test():
    """运行集成测试"""
    print("\n🔧 运行集成测试...")
    
    # 测试配置创建
    configs = {
        "安全模式": MultiTabConfig.create_safe_mode(),
        "默认模式": MultiTabConfig.create_default(),
        "快速模式": MultiTabConfig.create_fast_mode(),
        "单标签页": MultiTabConfig.create_single_tab_mode()
    }
    
    for mode_name, config in configs.items():
        print(f"   测试{mode_name}配置...")
        assert config.validate(), f"{mode_name}配置验证失败"
        
        if mode_name == "单标签页":
            assert not config.enable_multi_tab, "单标签页模式应该禁用多标签页"
            assert config.max_tabs == 1, "单标签页模式应该只有1个标签页"
        else:
            assert config.enable_multi_tab, f"{mode_name}应该启用多标签页"
            assert config.max_tabs > 1, f"{mode_name}应该有多个标签页"
    
    print("✅ 集成测试通过")


def demonstrate_improvements():
    """演示改进效果"""
    print("\n" + "=" * 60)
    print("🎯 多房型处理改进效果演示")
    print("=" * 60)
    
    print("\n📋 问题修复:")
    print("   ✅ 修复了房型处理循环中的状态冲突问题")
    print("   ✅ 改进了房型选择状态管理")
    print("   ✅ 添加了房型处理间的状态重置")
    print("   ✅ 增强了错误处理和恢复机制")
    
    print("\n🚀 新增功能:")
    print("   ✅ 多标签页并行处理架构")
    print("   ✅ 智能标签页管理和资源分配")
    print("   ✅ 灵活的配置模式选择")
    print("   ✅ 详细的性能监控和日志")
    
    print("\n📈 性能提升:")
    room_counts = [2, 4, 6, 8]
    tab_counts = [1, 2, 3, 5]
    
    print("   房型数量 | 单标签页 | 2标签页 | 3标签页 | 5标签页")
    print("   --------|----------|---------|---------|----------")
    
    for rooms in room_counts:
        times = []
        for tabs in tab_counts:
            if tabs == 1:
                time_needed = rooms * 30  # 30秒每房型
            else:
                batches = (rooms + tabs - 1) // tabs
                time_needed = batches * 30
            times.append(time_needed)
        
        print(f"   {rooms:2d}个房型  | {times[0]:4d}秒   | {times[1]:4d}秒  | {times[2]:4d}秒  | {times[3]:4d}秒")
    
    print("\n💡 使用建议:")
    print("   • 2-4个房型: 推荐使用安全模式 (2个标签页)")
    print("   • 4-6个房型: 推荐使用默认模式 (3个标签页)")
    print("   • 6个以上房型: 推荐使用快速模式 (5个标签页)")
    print("   • 调试阶段: 推荐使用单标签页模式")


def main():
    """主测试函数"""
    print("🧪 多房型处理修复验证测试")
    print("=" * 50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=0)
    
    # 运行集成测试
    run_integration_test()
    
    # 演示改进效果
    demonstrate_improvements()
    
    print("\n✅ 所有测试完成!")
    print("🎉 多房型处理问题已修复，多标签页功能已就绪!")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
适配简化日期格式的同程价格维护示例
处理页面显示格式：今日 7 8 9 10 11
"""

import datetime
import time
from tongcheng_price_maintain import PriceMaintainer
from DrissionPage import ChromiumPage

def main():
    """主程序：适配简化日期格式的价格维护"""
    
    # 配置参数
    hotel_code = "ucjawg449284"
    hotel_name = "香港丽思卡尔顿酒店"
    badazhou_hotel_room_name = '豪华客房(双床)'
    tongcheng_hotel_room_name = '豪华双床房'
    days = 30
    
    print("=== 适配简化日期格式的价格维护系统 ===")
    print(f"🏨 酒店: {hotel_name}")
    print(f"🏠 房型: {tongcheng_hotel_room_name}")
    print(f"📅 维护天数: {days}")
    print(f"📋 页面日期格式: 今日 7 8 9 10 11 (简化格式)")
    print()
    
    # 初始化
    page = ChromiumPage()
    maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)
    
    try:
        # 演示日期解析功能
        demo_date_parsing(maintainer)
        
        # 执行实际维护
        if input("是否执行实际的价格维护？(y/N): ").strip().lower() == 'y':
            run_maintenance(maintainer, hotel_code, days, badazhou_hotel_room_name)
        else:
            print("跳过实际维护，演示完成")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

def demo_date_parsing(maintainer):
    """演示日期解析功能"""
    
    print("📋 日期解析功能演示")
    print("-" * 40)
    
    # 模拟页面显示的日期
    sample_dates = ["今日", "7", "8", "9", "10", "11", "12", "13", "14", "15"]
    
    print("模拟页面表头日期:")
    print("列号:  ", " ".join([f"{i+2:3d}" for i in range(len(sample_dates))]))
    print("显示:  ", " ".join([f"{d:3s}" for d in sample_dates]))
    
    print("\n解析结果:")
    parsed_results = []
    for i, date_text in enumerate(sample_dates):
        parsed = maintainer.parse_page_date(date_text)
        parsed_results.append(parsed)
        print(f"  列{i+2:2d}: '{date_text:4s}' -> '{parsed}'")
    
    # 验证连续性
    print("\n连续性验证:")
    consecutive = True
    for i in range(1, len(parsed_results)):
        try:
            date1 = datetime.datetime.strptime(parsed_results[i-1], '%Y-%m-%d').date()
            date2 = datetime.datetime.strptime(parsed_results[i], '%Y-%m-%d').date()
            diff = (date2 - date1).days
            if diff == 1:
                print(f"  ✅ {date1.strftime('%m-%d')} -> {date2.strftime('%m-%d')} (连续)")
            else:
                print(f"  ⚠️  {date1.strftime('%m-%d')} -> {date2.strftime('%m-%d')} (间隔{diff}天)")
                consecutive = False
        except ValueError:
            print(f"  ❌ 日期格式错误")
            consecutive = False
    
    if consecutive:
        print("✅ 所有日期解析正确且连续")
    else:
        print("⚠️  日期解析存在问题，请检查")
    
    print()

def run_maintenance(maintainer, hotel_code, days, badazhou_hotel_room_name):
    """执行价格维护"""
    
    print("🔧 开始执行价格维护")
    print("-" * 40)
    
    try:
        # 步骤1: 获取价格数据
        print("📊 步骤1: 获取价格数据...")
        start_time = time.time()
        
        price_data = PriceMaintainer.get_first_room_price_data(hotel_code, days, badazhou_hotel_room_name)
        price_data = PriceMaintainer.fill_missing_dates(price_data, days)
        
        data_time = time.time() - start_time
        print(f"✅ 获取到{len(price_data)}天价格数据，耗时: {data_time:.1f}秒")
        
        # 显示价格数据概览
        valid_prices = [p for p in price_data.values() if p is not None]
        print(f"   📈 有效价格: {len(valid_prices)}天")
        print(f"   🚫 无效/关房: {len(price_data) - len(valid_prices)}天")
        
        # 步骤2: 测试页面日期获取
        print("\n📋 步骤2: 测试页面日期获取...")
        try:
            page_dates = maintainer.get_page_date_list(target_days=days)
            print(f"✅ 成功获取页面日期: {len(page_dates)}个")
            print(f"   首个日期: {page_dates[0] if page_dates else 'None'}")
            print(f"   最后日期: {page_dates[-1] if page_dates else 'None'}")
        except Exception as e:
            print(f"❌ 获取页面日期失败: {e}")
            return
        
        # 步骤3: 执行智能维护
        print("\n🔧 步骤3: 执行智能价格维护...")
        maintenance_start = time.time()
        
        changes_made = maintainer.smart_price_maintenance(price_data)
        
        maintenance_time = time.time() - maintenance_start
        
        # 步骤4: 结果统计
        total_cells = days * 4
        efficiency = ((total_cells - changes_made) / total_cells * 100) if total_cells > 0 else 0
        
        print(f"\n📈 维护结果:")
        print(f"   🎯 总格子数: {total_cells}")
        print(f"   ✏️  更新格子数: {changes_made}")
        print(f"   ⏭️  跳过格子数: {total_cells - changes_made}")
        print(f"   🚀 维护效率: {efficiency:.1f}% 无需更新")
        print(f"   ⏱️  维护耗时: {maintenance_time:.1f}秒")
        
        if changes_made == 0:
            print("✅ 所有价格已是最新，无需更新！")
        else:
            print(f"✅ 成功更新了 {changes_made} 个格子")
            
    except Exception as e:
        print(f"❌ 维护过程出错: {e}")
        import traceback
        traceback.print_exc()

def test_date_edge_cases():
    """测试日期边界情况"""
    
    print("🧪 日期边界情况测试")
    print("-" * 40)
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    today = datetime.date.today()
    
    print(f"当前日期: {today.strftime('%Y-%m-%d')} ({today.day}号)")
    
    # 测试各种边界情况
    test_cases = [
        ("今日", "当前日期"),
        (str(today.day + 1), "明天"),
        (str(today.day + 7), "一周后"),
        ("1", "下月1号"),
        ("31", "月末日期"),
        ("30", "30号"),
        ("29", "29号"),
    ]
    
    print("\n边界情况测试:")
    for date_input, description in test_cases:
        try:
            result = maintainer.parse_page_date(date_input)
            parsed_date = datetime.datetime.strptime(result, '%Y-%m-%d').date()
            days_diff = (parsed_date - today).days
            print(f"  {date_input:4s} ({description:8s}) -> {result} (距今{days_diff:+2d}天)")
        except Exception as e:
            print(f"  {date_input:4s} ({description:8s}) -> 错误: {e}")

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 完整演示（包含实际维护）")
    print("2. 仅日期解析演示")
    print("3. 边界情况测试")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        maintainer = PriceMaintainer(None, "test", "test", 30, "test")
        demo_date_parsing(maintainer)
    elif choice == "3":
        test_date_edge_cases()
    else:
        print("无效选择，运行默认演示")
        maintainer = PriceMaintainer(None, "test", "test", 30, "test")
        demo_date_parsing(maintainer)


import pprint

import requests
import json

url = "http://139.199.168.35:8080/getPrice"

payload = json.dumps({
  "hotelCode": "kvqwsi452900",
  "checkInDate": "2025-07-10",
  "checkOutDate": "2025-07-13",
  "personCount": "1",
  "roomCount": "1",
  "channel": "同城"
})
headers = {

  'Content-Type': 'application/json'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)


# pprint.pprint(response.text)

# 解析响应内容并输出指定格式的数据
try:
    resp_json = json.loads(response.text)
    data = resp_json.get('data', {})

    result = {
        "hotelCode": data.get("hotelCode", ""),
        "hotelName": data.get("hotelName", ""),
        "rooms": []
    }
    for room in data.get("roomList", []):
        room_info = {
            "roomName": room.get("roomName", ""),
            "roomCode": room.get("roomCode", ""),
            "averagePriceList": []
        }
        # 只取第一个 priceList 的 averagePriceList
        price_list = room.get("priceList", [])
        if price_list:
            avg_price_list = price_list[0].get("averagePriceList", [])
            room_info["averagePriceList"] = avg_price_list
        result["rooms"].append(room_info)
    print("\n解析后数据：")
    pprint.pprint(result)
except Exception as e:
    print("解析响应数据出错：", e)

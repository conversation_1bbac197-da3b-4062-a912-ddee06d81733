#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理优化示例
展示新的按页面批量处理逻辑，避免重复翻页
"""

import datetime
import time
from tongcheng_price_maintain import PriceMaintainer
from DrissionPage import ChromiumPage

def main():
    """主程序：展示批量处理优化"""
    
    # 配置参数
    hotel_code = "ucjawg449284"
    hotel_name = "香港丽思卡尔顿酒店"
    badazhou_hotel_room_name = '豪华客房(双床)'
    tongcheng_hotel_room_name = '豪华双床房'
    days = 30
    
    print("=== 批量处理优化演示 ===")
    print(f"🏨 酒店: {hotel_name}")
    print(f"🏠 房型: {tongcheng_hotel_room_name}")
    print(f"📅 维护天数: {days}")
    print(f"🚀 新特性: 按页面批量处理，避免重复翻页")
    print()
    
    # 初始化
    page = ChromiumPage()
    maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)
    
    try:
        # 演示批量处理逻辑
        demo_batch_processing()
        
        # 执行实际维护
        if input("是否执行实际的批量价格维护？(y/N): ").strip().lower() == 'y':
            run_batch_maintenance(maintainer, hotel_code, days, badazhou_hotel_room_name)
        else:
            print("跳过实际维护，演示完成")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

def demo_batch_processing():
    """演示批量处理逻辑"""
    
    print("📋 批量处理逻辑演示")
    print("=" * 50)
    
    # 模拟30天的更新场景
    today = datetime.date.today()
    date_list = [(today + datetime.timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30)]
    
    # 模拟需要更新的格子（分布在不同页面）
    sample_updates = [
        # 第1页更新（日期1-14）
        (date_list[2], 0, 1200, "今日调价"),
        (date_list[5], 1, None, "周末关房"),
        (date_list[10], 2, 1300, "价格调整"),
        
        # 第2页更新（日期15-28）  
        (date_list[15], 0, 1400, "周末涨价"),
        (date_list[18], 1, 1350, "价格优化"),
        (date_list[22], 3, None, "临时关房"),
        
        # 第3页更新（日期29-30）
        (date_list[28], 2, 999, "月末促销"),
    ]
    
    print("🔍 需要更新的格子分布:")
    page_groups = {}
    for date_str, row, price, reason in sample_updates:
        date_idx = date_list.index(date_str)
        page_idx = date_idx // 14
        if page_idx not in page_groups:
            page_groups[page_idx] = []
        page_groups[page_idx].append((date_str, row, price, reason))
    
    for page_idx in sorted(page_groups.keys()):
        updates = page_groups[page_idx]
        print(f"  第{page_idx + 1}页: {len(updates)} 个更新")
        for date_str, row, price, reason in updates:
            date_idx = date_list.index(date_str)
            print(f"    - 第{date_idx + 1}天 ({date_str}) 第{row+1}行: {reason}")
    
    print(f"\n📊 效率对比:")
    
    # 传统方式计算
    traditional_ops = 0
    for date_str, row, price, reason in sample_updates:
        date_idx = date_list.index(date_str)
        page_idx = date_idx // 14
        traditional_ops += page_idx * 2  # 翻页 + 回到第1页
    
    # 批量方式计算
    unique_pages = len(page_groups)
    batch_ops = unique_pages + (unique_pages - 1)  # 访问每页 + 回到第1页
    
    print(f"  传统方式: {len(sample_updates)} 个更新 × 平均翻页 = ~{traditional_ops} 次翻页操作")
    print(f"  批量方式: {unique_pages} 个页面 + 回到第1页 = {batch_ops} 次翻页操作")
    
    if traditional_ops > 0:
        improvement = ((traditional_ops - batch_ops) / traditional_ops * 100)
        print(f"  🚀 效率提升: {improvement:.1f}% (减少 {traditional_ops - batch_ops} 次翻页)")
    
    print(f"\n✅ 批量处理优势:")
    print(f"  ✓ 同一页面只访问一次")
    print(f"  ✓ 减少不必要的翻页操作")
    print(f"  ✓ 批量处理提高效率")
    print(f"  ✓ 详细的进度反馈")

def run_batch_maintenance(maintainer, hotel_code, days, badazhou_hotel_room_name):
    """执行批量价格维护"""
    
    print("\n🔧 开始批量价格维护")
    print("=" * 50)
    
    try:
        # 步骤1: 获取价格数据
        print("📊 步骤1: 获取价格数据...")
        start_time = time.time()
        
        price_data = PriceMaintainer.get_first_room_price_data(hotel_code, days, badazhou_hotel_room_name)
        price_data = PriceMaintainer.fill_missing_dates(price_data, days)
        
        data_time = time.time() - start_time
        print(f"✅ 获取到{len(price_data)}天价格数据，耗时: {data_time:.1f}秒")
        
        # 显示价格数据概览
        valid_prices = [p for p in price_data.values() if p is not None]
        print(f"   📈 有效价格: {len(valid_prices)}天")
        print(f"   🚫 无效/关房: {len(price_data) - len(valid_prices)}天")
        
        # 步骤2: 执行批量智能维护
        print(f"\n🚀 步骤2: 执行批量智能维护...")
        print(f"   特性: 按页面分组，避免重复翻页")
        
        maintenance_start = time.time()
        
        # 这里会自动使用新的批量处理逻辑
        changes_made = maintainer.smart_price_maintenance(price_data)
        
        maintenance_time = time.time() - maintenance_start
        
        # 步骤3: 结果统计
        total_cells = days * 4
        efficiency = ((total_cells - changes_made) / total_cells * 100) if total_cells > 0 else 0
        
        print(f"\n📈 批量维护结果:")
        print(f"   🎯 总格子数: {total_cells}")
        print(f"   ✏️  更新格子数: {changes_made}")
        print(f"   ⏭️  跳过格子数: {total_cells - changes_made}")
        print(f"   🚀 维护效率: {efficiency:.1f}% 无需更新")
        print(f"   ⏱️  维护耗时: {maintenance_time:.1f}秒")
        print(f"   📖 翻页优化: 按页面批量处理，大幅减少翻页次数")
        
        if changes_made == 0:
            print("✅ 所有价格已是最新，无需更新！")
        else:
            print(f"✅ 批量更新成功，共更新了 {changes_made} 个格子")
            
    except Exception as e:
        print(f"❌ 批量维护过程出错: {e}")
        import traceback
        traceback.print_exc()

def compare_methods():
    """对比新旧方法的差异"""
    
    print("\n📊 新旧方法对比")
    print("=" * 50)
    
    scenarios = [
        ("少量更新", 3, [0, 1, 2]),      # 3个更新都在第1页
        ("中等更新", 6, [0, 1, 15, 16, 28, 29]),  # 分布在3页
        ("大量更新", 12, list(range(0, 30, 3))),   # 分布在多页
    ]
    
    for scenario_name, update_count, date_indices in scenarios:
        print(f"\n{scenario_name} ({update_count}个更新):")
        
        # 计算页面分布
        pages_needed = set()
        for date_idx in date_indices:
            page_idx = date_idx // 14
            pages_needed.add(page_idx)
        
        # 传统方式：每个更新都要翻页查找
        traditional_ops = sum([(date_idx // 14) * 2 for date_idx in date_indices])
        
        # 批量方式：只访问需要的页面
        batch_ops = len(pages_needed) + max(0, len(pages_needed) - 1)
        
        print(f"  涉及页面: {sorted(pages_needed)} (共{len(pages_needed)}页)")
        print(f"  传统方式: {traditional_ops} 次翻页")
        print(f"  批量方式: {batch_ops} 次翻页")
        
        if traditional_ops > 0:
            improvement = ((traditional_ops - batch_ops) / traditional_ops * 100)
            print(f"  效率提升: {improvement:.1f}%")
        else:
            print(f"  效率提升: 无需翻页")

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 完整演示（包含实际维护）")
    print("2. 仅逻辑演示")
    print("3. 方法对比")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        demo_batch_processing()
    elif choice == "3":
        compare_methods()
    else:
        print("无效选择，运行默认演示")
        demo_batch_processing()

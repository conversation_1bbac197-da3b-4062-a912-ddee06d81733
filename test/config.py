#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程价格维护系统配置文件
"""

# 酒店基本信息
HOTEL_CONFIG = {
    "hotel_code": "ucjawg449284",
    "hotel_name": "香港丽思卡尔顿酒店",
    "days": 30,  # 维护天数
}

# 房型配置
ROOM_CONFIG = {
    # 八大洲系统的房型名称（用于获取价格数据）
    "badazhou_room_name": "豪华客房(双床)",
    
    # 同程系统的房型名称（用于页面操作）
    "tongcheng_room_name": "豪华双床房",
}

# 维护参数
MAINTENANCE_CONFIG = {
    "price_threshold": 0.05,  # 价格差异阈值（5%）
    "currency_rate": 0.91,    # 人民币转港币汇率
    "check_interval": 2,      # 检查间隔（小时）
    "max_retry": 3,           # 最大重试次数
}

# 页面配置
PAGE_CONFIG = {
    "max_pages": 3,           # 最大翻页数
    "page_load_timeout": 5,   # 页面加载超时（秒）
    "element_timeout": 2,     # 元素查找超时（秒）
}

# 日志配置
LOG_CONFIG = {
    "show_detailed_log": True,    # 显示详细日志
    "show_efficiency_stats": True, # 显示效率统计
    "log_price_changes": True,    # 记录价格变化
}

# 其他房型配置示例（可以根据需要添加更多）
OTHER_ROOM_CONFIGS = {
    "豪华大床房": {
        "badazhou_room_name": "豪华房（南楼）(大床)",
        "tongcheng_room_name": "豪华大床房 - 带阳台",
    },
    "豪华港景房": {
        "badazhou_room_name": "豪华港景客房（南楼）(大床)",
        "tongcheng_room_name": "豪华大床房（港景，带阳台）",
    },
}

def get_config():
    """获取完整配置"""
    return {
        "hotel": HOTEL_CONFIG,
        "room": ROOM_CONFIG,
        "maintenance": MAINTENANCE_CONFIG,
        "page": PAGE_CONFIG,
        "log": LOG_CONFIG,
    }

def print_config():
    """打印当前配置"""
    config = get_config()
    
    print("=== 当前配置 ===")
    print(f"酒店: {config['hotel']['hotel_name']}")
    print(f"酒店代码: {config['hotel']['hotel_code']}")
    print(f"维护天数: {config['hotel']['days']}")
    print()
    print(f"八大洲房型: {config['room']['badazhou_room_name']}")
    print(f"同程房型: {config['room']['tongcheng_room_name']}")
    print()
    print(f"价格阈值: {config['maintenance']['price_threshold']*100}%")
    print(f"汇率: {config['maintenance']['currency_rate']}")
    print(f"检查间隔: {config['maintenance']['check_interval']}小时")
    print()
    print(f"最大翻页数: {config['page']['max_pages']}")
    print(f"页面超时: {config['page']['page_load_timeout']}秒")
    print("=" * 30)

if __name__ == "__main__":
    print_config()

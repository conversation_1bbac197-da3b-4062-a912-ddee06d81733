import datetime
import time
import csv
from DrissionPage import ChromiumPage
from tongcheng.pusher.tongcheng.price_operator import TongchengPriceOperator
import requests
import json

# 配置参数
HOTEL_CODE = "kvqwsi452900"
HOTEL_NAME = "新加坡费尔蒙酒店"
BADAZHOU_HOTEL_ROOM_NAME = '特色沙龙套房（南楼）(大床)'
TONGCHENG_HOTEL_ROOM_NAME = '特色沙龙大床套房 - 带两个阳台'
DAYS = 30
STATS_CSV = "modify_stats.csv"

# 简单logger实现
class SimpleLogger:
    def info(self, msg):
        print(f"[INFO] {msg}")
    def warning(self, msg):
        print(f"[WARNING] {msg}")
    def error(self, msg):
        print(f"[ERROR] {msg}")

# 简单currency_converter实现
class SimpleCurrencyConverter:
    def cny_to_hkd(self, price_cny, rate=0.91):
        try:
            return int(round(float(price_cny) / rate))
        except Exception:
            return price_cny

# 获取页面表头日期列表
# 以页面实际显示的日期顺序为准，避免日期错位
def get_page_date_list(page):
    date_list = []
    for i in range(2, 16):  # th[2] 到 th[15]
        try:
            date_str = page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
            if date_str and date_str != '今日':
                date_list.append(date_str)
            elif date_str == '今日':
                today_str = datetime.date.today().strftime('%Y-%m-%d')
                date_list.append(today_str)
        except Exception:
            continue
    return date_list

# 构建目标价格矩阵（以页面日期为准）
def build_target_matrix(page_date_list, price_data, rows=4):
    n = len(page_date_list)
    matrix = [[9999 for _ in range(n)] for _ in range(rows)]
    for row in range(rows):
        for col, date_str in enumerate(page_date_list):
            price = price_data.get(date_str, None)
            real_price = 9999 if price is None else int(float(price))
            matrix[row][col] = real_price
    return matrix

# 获取价格数据（参考 tongcheng_price_maintain.py）
def get_first_room_price_data(hotel_code: str, days: int = 30, target_room_name="豪华房（南楼）(大床)") -> dict:
    result = {}
    today = datetime.date.today()
    url = "http://139.199.168.35:8080/getPrice"
    for i in range(days - 2):  # 最后两天无法再查三晚
        check_in = today + datetime.timedelta(days=i)
        check_out = check_in + datetime.timedelta(days=3)
        payload = json.dumps({
            "hotelCode": hotel_code,
            "checkInDate": check_in.strftime('%Y-%m-%d'),
            "checkOutDate": check_out.strftime('%Y-%m-%d'),
            "personCount": "1",
            "roomCount": "1",
            "channel": "同城"
        })
        headers = {'Content-Type': 'application/json'}
        response = requests.request("GET", url, headers=headers, data=payload)
        try:
            resp_json = json.loads(response.text)
            data = resp_json.get('data', {})
            room_list = data.get("roomList", [])
            if not room_list:
                continue
            target_room = None
            for room in room_list:
                if room.get("roomName") == target_room_name:
                    target_room = room
                    break
            if not target_room:
                print(f"未找到房型：{target_room_name}")
                continue
            price_list = target_room.get("priceList", [])
            if price_list:
                avg_price_list = price_list[0].get("averagePriceList", [])
                if avg_price_list:
                    for day_price in avg_price_list:
                        for date_str, price in day_price.items():
                            if date_str == check_in.strftime('%Y-%m-%d'):
                                result[date_str] = int(float(price))
        except Exception as e:
            print(f"解析接口数据出错: {e}")
    return result

def fill_missing_dates(price_data, days=30):
    today = datetime.date.today()
    filled = {}
    for i in range(days):
        date_str = (today + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
        filled[date_str] = price_data.get(date_str, None)
    return filled

# 统计并保存修改次数到csv
def save_modify_stats(modified_count, official_count, filename=STATS_CSV):
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open(filename, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        # 如果文件为空，写表头
        if f.tell() == 0:
            writer.writerow(['时间', '自动化修改次数', '官方需修改次数'])
        writer.writerow([now, modified_count, official_count])

def get_three_night_groups(page_date_list, price_data, actual_matrix):
    n = len(page_date_list)
    rows = 4
    groups = []
    for row in range(rows):
        for start in range(n - 2):
            if start + 2 >= len(actual_matrix[row]):
                continue
            dates = page_date_list[start:start+3]
            target_prices = [price_data.get(date, 9999) for date in dates]
            actual_prices = [actual_matrix[row][start + offset] for offset in range(3)]
            # 打印每组三晚连住的日期、表头xpath、价格格子xpath
            for offset in range(3):
                date = dates[offset]
                col = start + offset
                th_xpath = f'//*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{col+2}]/div'
                price_xpath = f'//*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col+2}]/div[3]'
                print(f"日期: {date}  表头xpath: {th_xpath}  价格格子xpath: {price_xpath}")
            groups.append({
                'row': row,
                'col': start,
                'dates': dates,
                'target_prices': target_prices,
                'actual_prices': actual_prices
            })
    return groups

def auto_modify_three_night_groups(page, operator, group_list, threshold=0.05):
    modified_count = 0
    for group in group_list:
        row = group['row']
        col = group['col']
        dates = group['dates']
        target_prices = group['target_prices']
        actual_prices = group['actual_prices']
        need_modify = False
        for t, a in zip(target_prices, actual_prices):
            if t in (None, 9999) or a in (None, 9999):
                continue
            if not operator.is_price_close(t, a, threshold):
                need_modify = True
                break
        if need_modify:
            print(f"第{row+1}行 {dates} 目标: {target_prices} 实际: {actual_prices}，有波动，自动化修改")
            for offset in range(3):
                td_col = col + offset
                if td_col > 16:
                    continue
                operator.fill_single_cell(row, td_col-2, td_col, target_prices[offset], dates[offset], 0)
            modified_count += 1
        else:
            print(f"第{row+1}行 {dates} 目标: {target_prices} 实际: {actual_prices}，无波动，不修改")
    return modified_count

# 主流程
def main():
    page = ChromiumPage()
    logger = SimpleLogger()
    currency_converter = SimpleCurrencyConverter()
    operator = TongchengPriceOperator(page, logger=logger, currency_converter=currency_converter)
    # 进入酒店页面（如有需要可补充自动化登录和跳转）
    # find_and_enter_hotel(page, HOTEL_NAME)
    while True:
        print("\n==============================")
        print(f"开始自动化填充流程，当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        # 获取本地价格数据
        price_data = get_first_room_price_data(HOTEL_CODE, DAYS, BADAZHOU_HOTEL_ROOM_NAME)
        price_data = fill_missing_dates(price_data, DAYS)
        # 采集页面表头日期
        page_date_list = get_page_date_list(page)
        print(f"页面表头日期: {page_date_list}")
        # 采集实际矩阵
        actual_matrix = operator.get_actual_price_matrix(page_date_list, TONGCHENG_HOTEL_ROOM_NAME)
        print("=== 实际采集到的价格矩阵 ===")
        for row in actual_matrix:
            print(' '.join([str(x) for x in row]))
        print("=== 实际价格矩阵打印结束 ===\n")
        group_list = get_three_night_groups(page_date_list, price_data, actual_matrix)
        for group in group_list:
            print(f"第{group['row']+1}行 {group['dates']} 目标: {group['target_prices']} 实际: {group['actual_prices']}")
        modified_count = auto_modify_three_night_groups(page, operator, group_list)
        save_modify_stats(modified_count, len(group_list))
        print(f"本次自动化修改三晚连住组数: {modified_count}，总组数: {len(group_list)}")
        print("==============================\n")
        time.sleep(2 * 60 * 60)  # 每2小时执行一次

if __name__ == "__main__":
    main()

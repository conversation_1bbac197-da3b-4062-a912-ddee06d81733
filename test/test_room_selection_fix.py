"""
房型选择修复测试脚本
验证多房型处理时的状态管理问题是否已解决
"""

import time
from tongcheng.pusher.tongcheng.tongcheng_pusher import TongchengPusher


def test_multi_room_processing():
    """测试多房型处理"""
    print("🧪 测试多房型处理修复效果")
    print("=" * 50)
    
    # 模拟多房型数据
    test_data = {
        "同程酒店名称": "香港丽思卡尔顿酒店",
        "房型数据": [
            {
                "同程房型名称": "豪华大床房（港景，带阳台）",
                "价格数据": [{"2025-07-07": "1200.00", "2025-07-08": "1250.00"}]
            },
            {
                "同程房型名称": "豪华双床房（港景）", 
                "价格数据": [{"2025-07-07": "1100.00", "2025-07-08": "1150.00"}]
            },
            {
                "同程房型名称": "行政套房（海景）",
                "价格数据": [{"2025-07-07": "2000.00", "2025-07-08": "2100.00"}]
            }
        ]
    }
    
    print(f"📋 测试数据:")
    print(f"   酒店: {test_data['同程酒店名称']}")
    print(f"   房型数量: {len(test_data['房型数据'])}")
    
    for i, room in enumerate(test_data['房型数据']):
        print(f"   房型{i+1}: {room['同程房型名称']}")
    
    print("\n🔧 修复要点:")
    print("   ✅ 每个房型处理前都会重新选择房型")
    print("   ✅ 改进的房型选择逻辑，检查选中状态")
    print("   ✅ 更好的错误处理和重试机制")
    print("   ✅ 详细的日志记录和状态跟踪")
    
    print("\n💡 使用说明:")
    print("   1. 确保已登录同程系统")
    print("   2. 确保cookies.json文件存在")
    print("   3. 运行测试观察房型选择过程")
    
    # 询问是否执行实际测试
    if input("\n是否执行实际的多房型处理测试？(y/N): ").strip().lower() == 'y':
        run_actual_test(test_data)
    else:
        print("跳过实际测试")


def run_actual_test(test_data):
    """运行实际测试"""
    print("\n🚀 开始实际测试...")
    
    try:
        # 创建推送器
        pusher = TongchengPusher(cookies_path="cookies.json")
        
        # 连接
        print("🔗 正在连接...")
        if not pusher.connect():
            print("❌ 连接失败")
            return
        
        print("✅ 连接成功")
        
        # 执行多房型处理
        print(f"\n📊 开始处理 {len(test_data['房型数据'])} 个房型...")
        
        start_time = time.time()
        result = pusher.push_price(test_data)
        end_time = time.time()
        
        # 显示结果
        print(f"\n📈 测试结果:")
        print(f"   状态: {'✅ 成功' if result else '❌ 失败'}")
        print(f"   总耗时: {end_time - start_time:.2f}秒")
        print(f"   平均每房型: {(end_time - start_time) / len(test_data['房型数据']):.2f}秒")
        
        if result:
            print(f"\n🎉 多房型处理测试成功!")
            print("   所有房型都被正确处理，状态冲突问题已解决")
        else:
            print(f"\n⚠️ 测试过程中出现问题")
            print("   请检查日志了解详细错误信息")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            pusher.disconnect()
            print("\n🧹 已断开连接")
        except:
            pass


def demonstrate_fix_details():
    """演示修复细节"""
    print("\n" + "=" * 60)
    print("🔧 房型选择修复详细说明")
    print("=" * 60)
    
    print("\n📋 问题分析:")
    print("   ❌ 原问题: 处理第一个房型正常，后续房型出现状态冲突")
    print("   ❌ 根本原因: 房型选择状态管理不当")
    print("   ❌ 具体表现: 后续房型没有被正确选中")
    
    print("\n🛠️ 修复方案:")
    print("   ✅ 在每个房型处理前强制重新选择房型")
    print("   ✅ 改进房型选择逻辑，检查当前选中状态")
    print("   ✅ 添加选择状态验证和重试机制")
    print("   ✅ 增强错误处理和日志记录")
    
    print("\n🔍 关键修改:")
    print("   1. tongcheng_pusher.py:")
    print("      - 添加 _ensure_room_selected() 方法")
    print("      - 在 _process_single_room() 中调用房型选择")
    print("      - 改进错误处理和状态跟踪")
    
    print("\n   2. price_operator.py:")
    print("      - 重写 select_room_by_name() 方法")
    print("      - 添加选中状态检查逻辑")
    print("      - 改进重试和错误恢复机制")
    
    print("\n📊 预期效果:")
    print("   ✅ 每个房型都能被正确选中和处理")
    print("   ✅ 房型间不会出现状态干扰")
    print("   ✅ 更好的错误恢复能力")
    print("   ✅ 详细的处理日志和状态跟踪")
    
    print("\n💡 使用建议:")
    print("   • 测试时观察日志中的房型选择过程")
    print("   • 确认每个房型都显示'已选中房型: xxx'")
    print("   • 如果仍有问题，检查房型名称是否完全匹配")
    print("   • 可以启用详细日志模式查看更多信息")


def main():
    """主函数"""
    print("🎯 房型选择修复验证工具")
    
    while True:
        print("\n请选择操作:")
        print("1. 查看修复详细说明")
        print("2. 运行多房型处理测试")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            demonstrate_fix_details()
        elif choice == "2":
            test_multi_room_processing()
        elif choice == "3":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()

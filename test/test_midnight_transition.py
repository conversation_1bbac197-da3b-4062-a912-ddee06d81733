#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试零点后时间窗口处理
验证过零点后页面还未更新时的日期解析逻辑
"""

import datetime
from tongcheng_price_maintain import PriceMaintainer

def test_midnight_transition_scenarios():
    """测试零点后时间窗口的各种场景"""
    
    print("=== 零点后时间窗口测试 ===")
    
    # 创建测试实例
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    # 模拟不同的时间窗口场景
    test_scenarios = [
        {
            "name": "正常情况 - 今天是7号",
            "today": datetime.date(2025, 7, 7),
            "page_display": ["今日", "8", "9", "10", "11", "12", "13"],
            "expected_first": "2025-07-07"
        },
        {
            "name": "零点后时间窗口 - 今天是7号，但第一列显示6",
            "today": datetime.date(2025, 7, 7),
            "page_display": ["6", "7", "8", "9", "10", "11", "12"],
            "expected_first": "2025-07-07"  # 应该修正为今天
        },
        {
            "name": "跨月情况 - 今天是8月1号，第一列显示31",
            "today": datetime.date(2025, 8, 1),
            "page_display": ["31", "1", "2", "3", "4", "5", "6"],
            "expected_first": "2025-08-01"  # 应该修正为今天
        },
        {
            "name": "月末情况 - 今天是7月31号",
            "today": datetime.date(2025, 7, 31),
            "page_display": ["今日", "1", "2", "3", "4", "5", "6"],
            "expected_first": "2025-07-31"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📋 测试场景: {scenario['name']}")
        print(f"   模拟今天: {scenario['today']}")
        print(f"   页面显示: {scenario['page_display']}")
        
        # 模拟日期解析
        results = []
        for i, date_text in enumerate(scenario['page_display']):
            col_idx = i + 2  # 列索引从2开始
            
            # 临时修改today用于测试
            original_today = datetime.date.today
            datetime.date.today = lambda: scenario['today']
            
            try:
                parsed = maintainer.parse_page_date(date_text, col_idx)
                results.append(parsed)
                print(f"     列{col_idx}: '{date_text}' -> '{parsed}'")
            except Exception as e:
                print(f"     列{col_idx}: '{date_text}' -> 错误: {e}")
                results.append(None)
            finally:
                # 恢复原始的today函数
                datetime.date.today = original_today
        
        # 验证第一个日期是否正确
        if results and results[0] == scenario['expected_first']:
            print(f"   ✅ 第一列解析正确: {results[0]}")
        else:
            print(f"   ❌ 第一列解析错误: 期望 {scenario['expected_first']}, 实际 {results[0] if results else 'None'}")
        
        # 验证日期连续性
        if len(results) > 1:
            consecutive = True
            for i in range(1, len(results)):
                if results[i] and results[i-1]:
                    try:
                        date1 = datetime.datetime.strptime(results[i-1], '%Y-%m-%d').date()
                        date2 = datetime.datetime.strptime(results[i], '%Y-%m-%d').date()
                        if (date2 - date1).days != 1:
                            consecutive = False
                            break
                    except ValueError:
                        consecutive = False
                        break
            
            if consecutive:
                print(f"   ✅ 日期序列连续")
            else:
                print(f"   ⚠️  日期序列不连续")

def test_smart_date_inference():
    """测试智能日期推断逻辑"""
    
    print("\n=== 智能日期推断测试 ===")
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    # 测试用例
    test_cases = [
        {
            "today": datetime.date(2025, 7, 7),
            "day_num": 6,
            "column_index": 2,  # 第一列
            "expected_behavior": "修正为今天",
            "description": "零点后时间窗口 - 第一列显示昨天"
        },
        {
            "today": datetime.date(2025, 7, 7),
            "day_num": 6,
            "column_index": 5,  # 非第一列
            "expected_behavior": "下个月6号",
            "description": "非第一列显示过去日期"
        },
        {
            "today": datetime.date(2025, 7, 31),
            "day_num": 1,
            "column_index": 3,
            "expected_behavior": "下个月1号",
            "description": "月末情况 - 下个月1号"
        },
        {
            "today": datetime.date(2025, 12, 31),
            "day_num": 1,
            "column_index": 3,
            "expected_behavior": "明年1月1号",
            "description": "跨年情况"
        }
    ]
    
    for case in test_cases:
        print(f"\n📋 {case['description']}")
        print(f"   今天: {case['today']}")
        print(f"   输入: 第{case['column_index']}列显示 '{case['day_num']}'")
        
        # 临时修改today用于测试
        original_today = datetime.date.today
        datetime.date.today = lambda: case['today']
        
        try:
            result = maintainer.smart_date_inference(case['day_num'], case['today'], case['column_index'])
            print(f"   结果: {result}")
            print(f"   期望: {case['expected_behavior']}")
            
            # 验证结果合理性
            if case['column_index'] == 2 and case['day_num'] == case['today'].day - 1:
                # 第一列且是昨天，应该修正为今天
                if result == case['today']:
                    print(f"   ✅ 正确修正为今天")
                else:
                    print(f"   ❌ 未正确修正")
            else:
                # 其他情况，检查是否为合理的未来日期
                if result >= case['today']:
                    print(f"   ✅ 结果为未来日期，合理")
                else:
                    print(f"   ⚠️  结果为过去日期，可能有问题")
                    
        except Exception as e:
            print(f"   ❌ 推断失败: {e}")
        finally:
            datetime.date.today = original_today

def test_midnight_detection():
    """测试零点后时间窗口检测"""
    
    print("\n=== 零点后时间窗口检测测试 ===")
    
    # 模拟不同的页面状态
    detection_cases = [
        {
            "first_col_text": "今日",
            "today": datetime.date(2025, 7, 7),
            "expected": False,
            "description": "正常状态 - 第一列显示今日"
        },
        {
            "first_col_text": "6",
            "today": datetime.date(2025, 7, 7),
            "expected": True,
            "description": "时间窗口 - 第一列显示昨天的数字"
        },
        {
            "first_col_text": "5",
            "today": datetime.date(2025, 7, 7),
            "expected": False,
            "description": "非时间窗口 - 第一列显示更早的数字"
        },
        {
            "first_col_text": "31",
            "today": datetime.date(2025, 8, 1),
            "expected": True,
            "description": "跨月时间窗口 - 8月1号但显示31"
        }
    ]
    
    for case in detection_cases:
        print(f"\n📋 {case['description']}")
        print(f"   第一列显示: '{case['first_col_text']}'")
        print(f"   今天: {case['today']}")
        
        # 模拟检测逻辑
        if case['first_col_text'] == '今日':
            detected = False
        else:
            try:
                first_day = int(case['first_col_text'])
                detected = (first_day == case['today'].day - 1)
            except ValueError:
                detected = False
        
        print(f"   检测结果: {'时间窗口' if detected else '正常状态'}")
        print(f"   期望结果: {'时间窗口' if case['expected'] else '正常状态'}")
        
        if detected == case['expected']:
            print(f"   ✅ 检测正确")
        else:
            print(f"   ❌ 检测错误")

def test_real_world_scenarios():
    """测试真实世界场景"""
    
    print("\n=== 真实场景测试 ===")
    
    # 模拟真实的时间窗口场景
    real_scenarios = [
        {
            "time": "2025-07-07 00:30",
            "description": "零点30分 - 刚过零点，页面还未更新",
            "page_display": ["6", "7", "8", "9", "10", "11", "12", "13", "14"],
            "should_correct_first": True
        },
        {
            "time": "2025-07-07 07:30", 
            "description": "早上7点30分 - 页面已更新",
            "page_display": ["今日", "8", "9", "10", "11", "12", "13", "14", "15"],
            "should_correct_first": False
        },
        {
            "time": "2025-08-01 01:00",
            "description": "8月1号凌晨1点 - 跨月时间窗口",
            "page_display": ["31", "1", "2", "3", "4", "5", "6", "7", "8"],
            "should_correct_first": True
        }
    ]
    
    maintainer = PriceMaintainer(None, "test", "test", 30, "test")
    
    for scenario in real_scenarios:
        print(f"\n📋 {scenario['description']}")
        print(f"   时间: {scenario['time']}")
        print(f"   页面显示: {scenario['page_display']}")
        
        # 解析时间
        time_obj = datetime.datetime.strptime(scenario['time'], '%Y-%m-%d %H:%M')
        today = time_obj.date()
        
        # 临时修改today
        original_today = datetime.date.today
        datetime.date.today = lambda: today
        
        try:
            # 解析第一列
            first_col = scenario['page_display'][0]
            parsed_first = maintainer.parse_page_date(first_col, 2)  # 第一列
            
            print(f"   第一列解析: '{first_col}' -> '{parsed_first}'")
            
            # 验证是否正确处理
            if scenario['should_correct_first']:
                if parsed_first == today.strftime('%Y-%m-%d'):
                    print(f"   ✅ 正确修正为今天")
                else:
                    print(f"   ❌ 未正确修正为今天")
            else:
                print(f"   ✅ 正常解析")
                
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")
        finally:
            datetime.date.today = original_today

if __name__ == "__main__":
    test_midnight_transition_scenarios()
    test_smart_date_inference()
    test_midnight_detection()
    test_real_world_scenarios()

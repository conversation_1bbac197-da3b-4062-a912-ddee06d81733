#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能价格维护功能测试脚本
用于验证新的基于日期匹配的价格维护功能
"""

import datetime
import time
from tongcheng_price_maintain import PriceMaintainer
from DrissionPage import ChromiumPage

def test_smart_maintenance():
    """测试智能价格维护功能"""
    
    # 配置参数
    hotel_code = "ucjawg449284"
    hotel_name = "香港丽思卡尔顿酒店"
    badazhou_hotel_room_name = '豪华客房(双床)'
    tongcheng_hotel_room_name = '豪华双床房'
    days = 7  # 测试用较少天数
    
    print("=== 智能价格维护功能测试 ===")
    print(f"酒店: {hotel_name}")
    print(f"房型: {tongcheng_hotel_room_name}")
    print(f"测试天数: {days}")
    print()
    
    # 初始化页面和维护器
    page = ChromiumPage()
    maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)
    
    try:
        # 测试1: 获取页面日期列表
        print("测试1: 获取页面日期列表")
        page_dates = maintainer.get_page_date_list(max_pages=2)
        print(f"获取到的日期: {page_dates}")
        print()
        
        # 测试2: 模拟价格数据
        print("测试2: 准备测试价格数据")
        today = datetime.date.today()
        test_price_data = {}
        for i in range(days):
            date_str = (today + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
            if i % 3 == 0:
                test_price_data[date_str] = 1200 + i * 10  # 有价格
            elif i % 3 == 1:
                test_price_data[date_str] = None  # 设为无效
            else:
                test_price_data[date_str] = 1300 + i * 5   # 有价格
        
        print("测试价格数据:")
        for date, price in test_price_data.items():
            print(f"  {date}: {price}")
        print()
        
        # 测试3: 获取实际价格矩阵
        print("测试3: 获取实际价格矩阵")
        date_list = list(test_price_data.keys())
        actual_matrix = maintainer.get_actual_price_matrix_by_date(date_list, rows=4)
        print("实际价格矩阵:")
        for date, prices in actual_matrix.items():
            print(f"  {date}: {prices}")
        print()
        
        # 测试4: 执行智能维护（模拟模式，不实际修改）
        print("测试4: 执行智能维护分析")
        print("注意: 这是分析模式，不会实际修改页面")
        
        # 生成目标矩阵
        target_matrix, date_list = maintainer.generate_price_matrix(test_price_data, rows=4)
        
        # 分析需要更新的格子
        changes_needed = 0
        total_cells = 0
        
        for row in range(4):
            for col, date_str in enumerate(date_list):
                total_cells += 1
                target_price = target_matrix[row][col]
                actual_prices = actual_matrix.get(date_str, [9999] * 4)
                actual_price = actual_prices[row] if row < len(actual_prices) else 9999
                
                # 判断是否需要更新
                need_update = False
                reason = ""
                
                if target_price is None or target_price == 9999:
                    if actual_price is not None and actual_price != 9999:
                        need_update = True
                        reason = "需设无效"
                elif actual_price is None or actual_price == 9999:
                    need_update = True
                    reason = "需设有效价格"
                else:
                    # 比较价格差异（转换为港币后比较）
                    target_hkd = maintainer.currency_cny_to_hkd(target_price)
                    if not maintainer.is_price_close(target_hkd, actual_price, 0.05):
                        need_update = True
                        reason = f"价格差异超过5%"
                
                if need_update:
                    print(f"  [需更新] 第{row+1}行 {date_str}: {reason} (目标:{target_price} 实际:{actual_price})")
                    changes_needed += 1
                else:
                    print(f"  [跳过] 第{row+1}行 {date_str}: 无需更新 (目标:{target_price} 实际:{actual_price})")
        
        print()
        print(f"分析结果:")
        print(f"  总格子数: {total_cells}")
        print(f"  需更新格子数: {changes_needed}")
        print(f"  跳过格子数: {total_cells - changes_needed}")
        print(f"  效率提升: {((total_cells - changes_needed) / total_cells * 100):.1f}% 的格子无需更新")
        
        # 测试5: 实际执行智能维护（可选）
        user_input = input("\n是否执行实际的智能维护？(y/N): ").strip().lower()
        if user_input == 'y':
            print("执行实际智能维护...")
            changes_made = maintainer.smart_price_maintenance(test_price_data)
            print(f"实际更新了 {changes_made} 个格子")
        else:
            print("跳过实际执行，测试完成")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n=== 测试完成 ===")

def test_date_matching():
    """测试日期匹配功能"""
    print("=== 日期匹配功能测试 ===")
    
    # 模拟不同的日期格式和"今日"情况
    test_cases = [
        "今日",
        "2025-07-07", 
        "2025-07-08",
        "昨日"
    ]
    
    today = datetime.date.today().strftime('%Y-%m-%d')
    
    for case in test_cases:
        if case == "今日":
            result = today
            print(f"'{case}' -> '{result}' (转换为今日日期)")
        elif case == "昨日":
            print(f"'{case}' -> 跳过 (忽略昨日)")
        else:
            print(f"'{case}' -> '{case}' (保持原样)")
    
    print("日期匹配测试完成\n")

if __name__ == "__main__":
    print("智能价格维护功能测试脚本")
    print("=" * 50)
    
    # 测试日期匹配
    test_date_matching()
    
    # 测试智能维护
    test_smart_maintenance()

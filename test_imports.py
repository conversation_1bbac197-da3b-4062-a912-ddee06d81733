#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入模块是否正常
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始测试导入...")

try:
    print("1. 测试基础模块导入...")
    from tongcheng.config.multi_process_config import MultiProcessConfig
    print("✅ MultiProcessConfig 导入成功")
    
    from tongcheng.config.multi_tab_config import MultiTabConfig
    print("✅ MultiTabConfig 导入成功")
    
    print("2. 测试工作流模块导入...")
    from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
    print("✅ MultiProcessOrchestrator 导入成功")
    
    from tongcheng.workflow.process_manager import ProcessManager
    print("✅ ProcessManager 导入成功")
    
    from tongcheng.workflow.workflow import WorkflowCoordinator
    print("✅ WorkflowCoordinator 导入成功")
    
    from tongcheng.workflow.error_handler import ErrorHandler
    print("✅ ErrorHandler 导入成功")
    
    from tongcheng.workflow.performance_monitor import PerformanceMonitor
    print("✅ PerformanceMonitor 导入成功")
    
    print("3. 测试推送器模块导入...")
    from tongcheng.pusher.tongcheng.multi_tab_processor import MultiTabTongchengPusher
    print("✅ MultiTabTongchengPusher 导入成功")
    
    print("4. 测试日志模块导入...")
    from tongcheng.tools.logger import get_workflow_logger
    print("✅ get_workflow_logger 导入成功")
    
    print("\n🎉 所有模块导入测试通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {str(e)}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 其他错误: {str(e)}")
    import traceback
    traceback.print_exc()

# 同程酒店价格同步系统

这是一个高性能的自动化酒店价格同步系统，主要功能包括：
1. 从飞书获取酒店信息和房型映射关系
2. 从八大洲接口获取酒店价格数据
3. 进行房型匹配和数据处理
4. 将匹配后的价格数据推送到同程平台

## ✨ 新特性：多进程多标签页版本

- **🚀 大幅性能提升**: 多进程 + 多标签页并行处理，理论加速比可达 15x
- **🔧 解决状态冲突**: 房型在独立标签页中处理，彻底解决状态干扰问题
- **🔄 智能错误恢复**: 单个酒店或房型失败不影响其他处理
- **📊 详细监控**: 实时进程和性能监控，资源使用透明
- **⚙️ 灵活配置**: 多种预设模式，支持自定义配置

## 快速开始

### 多进程多标签页版本（推荐）
```bash
# 启动多进程版本
python tongcheng/main_multi_process.py

# 或使用快速启动脚本
python tongcheng/start_multi_process.py
```

### 标准版本（单进程）
```bash
# 启动标准版本
python tongcheng/main.py
```

---

# tongcheng 项目详细说明

## 目录结构

```
tongcheng/
│
├── main.py                # 主程序入口（当前为空）
├── orchestrator.py        # 主控流程/调度器（当前为空）
├── config_manager.py      # 配置管理（当前为空）
│
├── models/                # 数据模型相关
│   ├── __init__.py
│   ├── config_models.py
│   ├── sync_models.py
│   ├── mapping_models.py
│   ├── hotel_models.py
│   ├── price_models.py
│   └── base_models.py
│
├── pusher/                # 推送相关功能
│   ├── tongcheng_pusher.py
│   └── base_pusher.py
│
├── matcher/               # 匹配相关功能
│   ├── csv_matcher.py
│   ├── feishu_matcher.py
│   └── base_matcher.py
│
├── transfomer/            # 数据转换相关功能
│   ├── badazhou_transformer.py
│   └── base_transformer.py
│
├── fetcher/               # 数据抓取相关功能
│   ├── badazhou_fetcher.py
│   └── base_fetcher.py
│
├── tools/                 # 工具类（当前为空）
├── config/                # 配置文件目录（当前为空）
└── logs/                  # 日志目录（当前为空）
```

## 各模块简介

- **main.py**
  项目主入口，负责启动整体流程。

- **orchestrator.py**
  负责调度各个子模块的执行顺序和数据流转。

- **config_manager.py**
  管理和加载项目配置。

- **models/**
  定义项目中用到的各种数据模型，如酒店、价格、同步、映射等。

- **pusher/**
  实现数据推送相关逻辑，支持不同推送方式的扩展。

- **matcher/**
  实现数据匹配相关逻辑，如 CSV 匹配、飞书匹配等。

- **transformer/**
  实现数据转换相关逻辑，便于不同数据源格式的适配。

- **fetcher/**
  实现数据抓取相关逻辑，支持不同数据源的抓取扩展。

- **tools/**
  存放通用工具类或函数（当前为空）。

- **config/**
  存放配置文件（当前为空）。

- **logs/**
  存放日志文件（当前为空）。

## 2024-06-19 新增

- 新增函数：`fill_three_rows_price_with_block_day_on_page(page, price_data)`
    - 功能：三行斜对角填充价格，每一行每3格填同一个价格（分别是当天、前一天、前两天的价格），第4格自动填9999（关房），循环进行，三行都按此规则填充。
    - 用法示例：
      ```python
      fill_three_rows_price_with_block_day_on_page(page, price_data)
      ```
    - 适用场景：需要实现"只能订3晚，不能订4晚及以上"的房态维护需求。
    - 详细规则：
      - 第1行：每3格都填当天的价格，第4格填9999
      - 第2行：每3格都填前一天的价格，第4格填9999
      - 第3行：每3格都填前两天的价格，第4格填9999
      - 这样循环，三行都按此规则填充
    - 原有函数和逻辑不变。

## 2025-01-11 新增 - 完整工作流程实现

### 配置文件改动
- **新增**: `tongcheng/config/feishu_config.json`
  - 配置飞书酒店表格和房型表格的连接参数
  - 配置重试机制参数（最大重试次数、重试间隔）
  - 配置查询天数（默认30天）

- **更新**: `tongcheng/config_manager.py`
  - 新增 `get_feishu_config()` 函数，用于读取飞书配置

### 工作流程实现
- **重大更新**: `tongcheng/workflow/orchestrator.py`
  - 实现完整的数据处理工作流程：
    1. 从飞书获取酒店信息（酒店编码、八大洲酒店名称、同程酒店名称）
    2. 从飞书获取房型匹配关系
    3. 使用酒店编码调用八大洲接口获取未来30天价格数据
    4. 进行房型匹配（八大洲房型 -> 同程房型）
    5. 推送匹配后的数据到同程平台
  - 实现带重试机制的错误处理
  - 实现详细的日志记录和执行报告
  - 实现监控告警机制（失败达到最大重试次数时发送告警）
  - 新增 `TongchengPusher` 类作为同程推送器基础实现

### 核心功能特性
- **配置化管理**: 所有参数通过配置文件管理，便于维护
- **重试机制**: 接口调用失败时自动重试，达到最大次数后发送监控告警
- **错误记录**: 详细记录各种错误类型和原因
- **房型匹配**: 智能匹配八大洲房型到同程房型，跳过无匹配的房型
- **日期管理**: 使用time模块获取当前日期，查询未来指定天数的价格
- **日志系统**: 同时输出到文件和控制台，包含时间戳和详细信息
- **执行报告**: 生成详细的执行统计报告，包含处理数量、成功/失败数量等

### 使用方法

### 标准版本（单进程）
```python
from tongcheng.workflow.orchestrator import Orchestrator

# 创建调度器实例
orchestrator = Orchestrator()

# 执行完整工作流程
orchestrator.main_workflow()
```

### 多进程多标签页版本（推荐）
```bash
# 启动多进程版本
python tongcheng/main_multi_process.py

# 或使用快速启动脚本
python tongcheng/start_multi_process.py
```

```python
# 编程方式使用
from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import DEFAULT_MULTI_PROCESS_CONFIG

# 创建多进程协调器
orchestrator = MultiProcessOrchestrator(DEFAULT_MULTI_PROCESS_CONFIG)

# 执行多进程工作流程
result = orchestrator.main_workflow()
```

## 2025-01-11 重构 - 模块化架构优化

### 重构目标
将 `orchestrator.py` 中的各个功能模块分解到不同文件中，实现更好的职责分离和可维护性。

### 新增模块文件
- **新增**: `tongcheng/workflow/data_processor.py`
  - 数据处理器，负责数据匹配、转换等逻辑处理
  - 包含房型匹配逻辑、数据验证、日期范围获取等功能
  - 支持去重、过滤等数据处理操作

- **新增**: `tongcheng/workflow/pusher_manager.py`
  - 推送管理器，管理各种推送器和推送逻辑
  - 包含 `TongchengPusher` 同程推送器实现
  - 集成错误处理和性能监控

### 重构现有模块
- **重大重构**: `tongcheng/workflow/error_handler.py`
  - 实现完整的错误处理功能
  - 包含错误记录、监控告警、重试机制等
  - 支持错误统计和清理功能

- **重大重构**: `tongcheng/workflow/performance_monitor.py`
  - 实现性能监控和执行报告生成
  - 支持统计计数器、执行时间跟踪
  - 生成详细的执行报告

- **重大重构**: `tongcheng/workflow/scheduler.py`
  - 实现任务调度功能
  - 支持单个任务和批量任务调度
  - 包含任务状态管理和执行结果跟踪

- **重大重构**: `tongcheng/workflow/workflow.py`
  - 工作流程协调器，整合各个模块
  - 实现完整的主工作流程逻辑
  - 负责组件初始化和流程编排

- **重大重构**: `tongcheng/workflow/orchestrator.py`
  - 简化为主调度器，负责系统整体协调
  - 集成六大管理模块
  - 提供系统状态监控和健康检查功能

### 架构优化成果
- **模块化设计**: 每个文件职责单一，便于维护和扩展
- **依赖注入**: 组件间通过依赖注入方式协作，降低耦合度
- **错误隔离**: 各模块错误不会影响其他模块运行
- **可测试性**: 每个模块可独立测试
- **可扩展性**: 新功能可以轻松添加到对应模块中

### 新的使用方法
```python
from tongcheng.workflow.orchestrator import Orchestrator

# 创建调度器实例（自动初始化所有模块）
orchestrator = Orchestrator()

# 执行完整工作流程
result = orchestrator.main_workflow()

# 获取系统状态
status = orchestrator.get_system_status()

# 系统健康检查
health = orchestrator.health_check()

# 重置系统（清理状态）
orchestrator.reset_system()
```

### 模块间关系
```
Orchestrator (主调度器)
├── Scheduler (任务调度)
├── ErrorHandler (错误处理)
├── PerformanceMonitor (性能监控)
├── StateManager (状态管理)
├── ResourceManager (资源管理)
└── WorkflowCoordinator (工作流协调)
    ├── DataProcessor (数据处理)
    ├── PusherManager (推送管理)
    ├── HotelNameGetter (飞书酒店数据)
    ├── RoomNameGetter (飞书房型数据)
    └── BadazhouFetcher (八大洲数据获取)
```

## 2025-01-11 整合与完善 - 系统集成和主程序实现

### 问题修复
- **修复**: `tongcheng/config_manager.py`
  - 修复了缺失的 `get_feishu_config()` 函数
  - 确保配置管理器功能完整性

### 数据处理器整合
- **重大整合**: `tongcheng/workflow/data_processor.py`
  - 整合现有的 `BadazhouProcessor` 数据处理器
  - 新增 `process_badazhou_data()` 方法，使用标准化的数据处理流程
  - 支持Hotel和RoomType模型的数据转换
  - 实现数据格式的双向转换（Model对象 ↔ 字典格式）

- **更新**: `tongcheng/workflow/workflow.py`
  - 更新工作流程，使用集成的数据处理器
  - 调用 `process_badazhou_data()` 进行标准化数据处理
  - 改用 `extract_rooms_from_processed_data()` 提取房型信息

### 主程序实现
- **重大实现**: `tongcheng/main.py`
  - 实现完整的一键启动功能
  - 包含美观的启动横幅和执行报告
  - 支持两种运行模式：
    1. **默认模式**: `python main.py` - 一键执行完整工作流程
    2. **交互模式**: `python main.py --interactive` - 提供菜单选择功能
  - 完善的异常处理和日志记录
  - 实时显示执行进度和结果统计
  - 优雅的错误提示和系统状态展示

### 核心特性增强
- **智能数据处理**: 使用现有的BadazhouProcessor确保数据处理的一致性和可靠性
- **用户友好界面**: 提供清晰的控制台输出和交互式操作
- **完整的错误处理**: 从配置加载到数据推送的全链路错误捕获
- **状态监控**: 实时监控系统运行状态和性能指标
- **灵活启动方式**: 支持命令行直接执行和交互式菜单操作

### 使用方法

#### 1. 一键启动（推荐）
```bash
# 进入项目目录
cd tongcheng

# 一键执行完整工作流程
python main.py
```

#### 2. 交互模式
```bash
# 启动交互式菜单
python main.py --interactive

# 菜单选项：
# 1. 执行完整工作流程
# 2. 系统健康检查
# 3. 查看系统状态
# 4. 重置系统状态
# 5. 退出程序
```

#### 3. 程序化调用
```python
from tongcheng.workflow.orchestrator import Orchestrator

# 创建并运行系统
orchestrator = Orchestrator()
result = orchestrator.main_workflow()
print(result)
```

### 执行报告示例
```
============================================================
                      执行报告
============================================================
执行时间: 2025-01-11 10:30:00 - 2025-01-11 10:35:00
执行时长: 300.00秒

处理统计:
  处理酒店数量: 5
  成功匹配房型: 23
  失败匹配房型: 2
  推送成功数量: 23
  推送失败数量: 0

✅ 没有错误发生
============================================================

🎉 同程酒店价格同步任务执行完成！
```

### 系统架构最终形态
```
main.py (程序入口)
    └── Orchestrator (系统调度器)
        ├── WorkflowCoordinator (工作流协调)
        │   ├── DataProcessor (数据处理)
        │   │   └── BadazhouProcessor (八大洲数据处理器)
        │   ├── PusherManager (推送管理)
        │   ├── HotelNameGetter (飞书酒店数据)
        │   ├── RoomNameGetter (飞书房型数据)
        │   └── BadazhouFetcher (八大洲数据获取)
        ├── ErrorHandler (错误处理)
        ├── PerformanceMonitor (性能监控)
        ├── Scheduler (任务调度)
        ├── StateManager (状态管理)
        └── ResourceManager (资源管理)
```

## 2025-01-11 推送模块实现 - 完整的同程价格推送系统

### 推送模块架构
基于现有的同程价格维护逻辑，实现了完整的自动化推送系统，整合了DrissionPage浏览器自动化技术。

### 新增推送器模块
- **新增**: `tongcheng/pusher/base_pusher.py`
  - 基础推送器抽象类，定义推送接口规范
  - 包含数据验证、日志记录、错误处理等通用功能
  - 提供 `push_price()`、`connect()`、`disconnect()` 等抽象方法
  - 实现推送状态管理和错误消息格式化

- **重大实现**: `tongcheng/pusher/tongcheng_pusher.py`
  - 完整的同程推送器实现，集成DrissionPage自动化技术
  - 实现Cookie登录、酒店搜索、价格填充等完整流程
  - 支持"三行斜对角三晚连住+关房经典填法"价格策略
  - 包含自动翻页、弹窗处理、价格无效设置等高级功能
  - 集成错误重试机制和详细日志记录

- **新增**: `tongcheng/pusher/tongcheng_rule1_pusher.py`
  - 基于Rule1规则的简化推送器
  - 使用简单的顺序填充策略，只填充第一行价格
  - 适用于不需要复杂斜对角填法的场景
  - 继承自TongchengPusher，重写价格填充策略

### 推送管理器增强
- **重大更新**: `tongcheng/workflow/pusher_manager.py`
  - 移除了临时的TongchengPusher实现，使用专业的推送器
  - 集成连接管理，支持自动连接和断开
  - 新增批量推送功能 `push_multiple_hotels()`
  - 实现推送器状态监控和连接重置功能
  - 支持上下文管理器，确保资源正确释放

### 核心推送功能

#### 1. 自动化登录系统

```python
# 支持Cookie文件登录
pusher = TongchengPusher(cookies_path="script/cookies.json")
pusher.connect()  # 自动读取cookies并登录同程平台
```

#### 2. 智能酒店查找
- 自动搜索并进入指定酒店的房型维护页面
- 处理下拉选项选择和页面跳转
- 包含重试机制和错误处理

#### 3. 高级价格填充策略

**默认策略：三行斜对角三晚连住+关房填法**
```
第1行：[价格][价格][价格][关房][价格][价格][价格][关房]...
第2行：    [价格][价格][价格][关房][价格][价格][价格][关房]...
第3行：        [价格][价格][价格][关房][价格][价格][价格]...
```

**Rule1策略：简单顺序填充**
```
第1行：[价格][价格][价格][价格][价格][价格]...（按日期顺序）
```

#### 4. 智能页面操作
- 自动翻页管理（每14格点击"后两周"）
- 弹窗自动关闭处理
- 价格无效状态自动设置（8888、99999价格）
- 页面元素智能定位和重试

### 数据格式支持
```python
# 推送数据格式
push_data = {
    "同程酒店名称": "新加坡费尔蒙酒店",
    "房型数据": [
        {
            "八大洲房型名称": "豪华房（南楼）(大床)",
            "八大洲房型编码": "198831",
            "同程房型名称": "豪华大床房",
            "价格数据": [{"2025-01-12": "3021", "2025-01-13": "3021"}]
        }
    ]
}
```

### 错误处理和监控
- **连接错误**: 自动重试机制，达到最大次数后发送监控告警
- **元素定位失败**: 智能重试和备用定位策略
- **价格填充失败**: 详细错误记录，不影响其他价格填充
- **推送状态监控**: 实时跟踪推送进度和成功率

### 使用方法

#### 1. 直接使用推送器

```python
from tongcheng.pusher.tongcheng.tongcheng_pusher import TongchengPusher

# 创建推送器
pusher = TongchengPusher(cookies_path="script/cookies.json")

# 连接并推送
if pusher.connect():
    result = pusher.push_price(push_data)
    pusher.disconnect()
```

#### 2. 通过推送管理器

```python
from tongcheng.workflow.pusher_manager import PusherManager

# 使用上下文管理器
with PusherManager(cookies_path="script/cookies.json") as manager:
    result = manager.push_price_data(hotel_name, matched_rooms)
```

#### 3. 系统集成使用
推送器已完全集成到主工作流程中，通过 `python main.py` 即可自动执行推送。

### 配置要求
- **必需文件**: `test/cookies.json` - 同程平台的登录cookies
- **依赖环境**: DrissionPage、ChromiumPage浏览器驱动
- **网络要求**: 能够访问同程平台 (`https://ebooking.elong.com`)

### 推送器对比

| 特性 | TongchengPusher | TongchengRule1Pusher |
|------|-----------------|---------------------|
| 价格策略 | 三行斜对角+关房 | 简单顺序填充 |
| 适用场景 | 3晚连住+限制4晚以上 | 简单价格维护 |
| 复杂度 | 高 | 低 |
| 推荐使用 | 生产环境 | 测试/简单场景 |

### 系统架构更新
```
PusherManager (推送管理器)
├── TongchengPusher (主推送器)
│   ├── Cookie登录系统
│   ├── 酒店搜索引擎
│   ├── 价格填充引擎
│   │   ├── 三行斜对角策略
│   │   ├── 自动翻页管理
│   │   ├── 弹窗处理
│   │   └── 无效价格设置
│   └── 连接管理器
└── TongchengRule1Pusher (简化推送器)
    └── 简单顺序填充策略
```

现在系统具备了完整的自动化价格推送能力，可以自动登录同程平台、搜索酒店、填充价格，并处理各种异常情况，实现了真正的无人值守价格同步。

## 2025-01-11 系统优化 - 统一日志管理和数据模型应用

### 日志系统统一化
- **新增**: `tongcheng/tools/logger.py`
  - 实现统一的日志管理器 `LoggerManager`
  - 支持多种日志输出：控制台、文件、错误日志分离
  - 提供模块化日志器：工作流、推送器、抓取器、匹配器、处理器专用日志器
  - 支持日志轮转、大小限制、备份管理
  - 包含性能日志、错误上下文记录、系统信息记录等高级功能
  - 提供便捷函数：`setup_logging()`、`get_logger()`、`get_workflow_logger()` 等

### 日志管理器核心特性
```python
# 统一配置
setup_logging(
    log_level="INFO",
    log_dir="logs",
    console_output=True,
    file_output=True,
    max_bytes=10*1024*1024,  # 10MB
    backup_count=5
)

# 模块专用日志器
workflow_logger = get_workflow_logger()    # 工作流日志
pusher_logger = get_pusher_logger()        # 推送器日志
fetcher_logger = get_fetcher_logger()      # 抓取器日志
matcher_logger = get_matcher_logger()      # 匹配器日志
processor_logger = get_processor_logger()  # 处理器日志

# 高级日志功能
LoggerManager.log_system_info("ComponentName", "Action", extra_info="详情")
LoggerManager.log_error_with_context("ComponentName", exception, {"context": "value"})
LoggerManager.log_performance("ComponentName", "Action", 1.23, processed_items=100)
```

### 系统模块日志更新
- **更新**: `tongcheng/main.py`
  - 替换为统一日志管理器 `LoggerManager`
  - 使用 `get_logger("Main")` 和 `get_logger("MainInteractive")`
  - 移除原有的basicConfig日志配置

- **更新**: `tongcheng/workflow/orchestrator.py`
  - 使用 `get_workflow_logger()` 统一工作流日志
  - 移除原有的日志配置代码

- **更新**: `tongcheng/workflow/workflow.py`
  - 应用统一日志管理器和数据模型导入
  - 新增数据模型支持：`Hotel`、`RoomType`、`RoomMapping`、`PriceCalendar`

- **更新**: `tongcheng/workflow/pusher_manager.py`
  - 使用 `get_pusher_logger()` 专用推送日志器

- **更新**: `tongcheng/workflow/error_handler.py`
  - 使用 `LoggerManager.get_logger("ErrorHandler")` 错误处理专用日志器

- **更新**: `tongcheng/workflow/data_processor.py`
  - 使用 `get_processor_logger()` 处理器专用日志器
  - 新增数据模型导入：`Hotel`、`RoomType`、`RoomMapping`、`PriceCalendar`

- **更新**: `tongcheng/pusher/base_pusher.py`
  - 使用 `get_pusher_logger()` 统一推送器日志

- **更新**: `tongcheng/fetcher/badazhou_fetcher.py`
  - 使用 `get_fetcher_logger()` 抓取器专用日志器
  - 新增构造函数和详细日志记录
  - 在 `fetch_price_range()` 方法中添加批量处理日志和错误处理

### 数据模型应用
- **数据模型支持**: 各核心模块现在导入并支持数据模型
  - `Hotel`：酒店模型，包含ID、名称、房型列表
  - `RoomType`：房型模型，包含ID、名称、价格日历
  - `PriceCalendar`：价格日历模型，记录日期和基础价格
  - `RoomMapping`：房型映射模型，定义映射关系
  - `BaseEntity`、`PlatformType`、`MappingStatus`：基础模型和枚举

### 日志输出优化

#### 专用日志文件
- `logs/tongcheng_main.log` - 主系统日志（轮转）
- `logs/tongcheng_error.log` - 错误专用日志
- `logs/workflow.log` - 工作流专用日志
- `logs/pusher.log` - 推送器专用日志
- `logs/fetcher.log` - 抓取器专用日志
- `logs/matcher.log` - 匹配器专用日志
- `logs/processor.log` - 处理器专用日志

#### 日志格式标准化
```
2025-01-11 10:30:00 - ModuleName - INFO - filename.py:123 - 详细消息内容
```

### 性能和错误监控增强
- **性能监控**: 自动记录组件执行时间和处理量
- **错误上下文**: 错误记录包含完整上下文信息和堆栈跟踪
- **系统信息**: 关键操作的系统状态记录
- **分级日志**: 不同级别的日志分别输出到对应文件

### 向后兼容性
- 保持所有原有功能不变
- 现有的日志调用自动迁移到新的日志管理器
- 配置文件和接口保持兼容

### 使用方法

#### 1. 在新模块中使用
```python
from tongcheng.tools.logger import get_workflow_logger, LoggerManager

# 获取专用日志器
logger = get_workflow_logger()
logger.info("工作流开始执行")

# 使用高级日志功能
LoggerManager.log_performance("DataProcessor", "房型匹配", 2.5, matched_count=45)
```

#### 2. 系统初始化
```python
from tongcheng.tools.logger import setup_logging

# 程序启动时配置日志
setup_logging(
    log_level="INFO",
    log_dir="logs",
    console_output=True,
    file_output=True
)
```

### 优化成果
- **统一管理**: 所有模块使用相同的日志配置和格式
- **性能提升**: 减少日志配置重复，提高系统启动速度
- **可维护性**: 集中的日志配置，便于全局调整
- **可监控性**: 专用日志文件便于系统监控和问题排查
- **标准化**: 统一的数据模型确保数据结构一致性
- **可扩展性**: 新增模块可以轻松接入统一日志系统

### 系统架构最终更新
```
LoggerManager (统一日志管理器)
├── 全局日志配置
├── 模块专用日志器
│   ├── WorkFlow Logger
│   ├── Pusher Logger
│   ├── Fetcher Logger
│   ├── Matcher Logger
│   └── Processor Logger
├── 高级日志功能
│   ├── 性能监控日志
│   ├── 错误上下文日志
│   └── 系统信息日志
└── 日志文件管理
    ├── 主日志文件（轮转）
    ├── 错误日志文件
    └── 模块专用日志文件

DataModels (数据模型系统)
├── Hotel (酒店模型)
├── RoomType (房型模型)
├── PriceCalendar (价格日历)
├── RoomMapping (房型映射)
└── BaseModels (基础模型)
```

现在系统具备了企业级的日志管理能力和标准化的数据模型支持，为后续的监控、运维和扩展提供了强大的基础设施。

---

# 多进程多标签页版本详细说明

## 概述

多进程多标签页版本是同程酒店价格同步系统的高性能版本，通过以下技术实现大幅性能提升：

- **多进程并行**: 不同酒店在不同进程中并行处理
- **多标签页并行**: 每个进程内的房型在不同标签页中并行处理
- **智能资源管理**: 自动管理进程和标签页资源
- **错误隔离**: 单个酒店或房型的错误不影响其他处理

## 性能对比

假设处理6个酒店，每个酒店4个房型，每个房型需要30秒：

| 模式 | 进程数 | 标签页数 | 理论并行度 | 预计时间 | 加速比 |
|------|--------|----------|------------|----------|--------|
| 原版单线程 | 1 | 1 | 1x | 12分钟 | 1.0x |
| 安全模式 | 2 | 2 | 4x | 3分钟 | 4.0x |
| 默认模式 | 2 | 3 | 6x | 2分钟 | 6.0x |
| 快速模式 | 3 | 5 | 15x | 48秒 | 15.0x |

## 主要文件

### 核心文件
- `tongcheng/main_multi_process.py` - 多进程主程序
- `tongcheng/start_multi_process.py` - 快速启动脚本
- `tongcheng/test_multi_process.py` - 测试脚本

### 配置文件
- `tongcheng/config/multi_process_config.py` - 多进程配置
- `tongcheng/config/multi_tab_config.py` - 多标签页配置

### 核心组件
- `tongcheng/workflow/multi_process_orchestrator.py` - 多进程协调器
- `tongcheng/workflow/process_manager.py` - 进程管理器
- `tongcheng/pusher/tongcheng/multi_tab_processor.py` - 多标签页处理器

### 文档和示例
- `docs/multi_process_guide.md` - 详细使用指南
- `docs/multi_tab_architecture.md` - 多标签页架构说明
- `examples/multi_process_example.py` - 使用示例

## 使用方法

### 1. 快速开始

```bash
# 直接运行多进程版本
python tongcheng/main_multi_process.py

# 或使用快速启动脚本
python tongcheng/start_multi_process.py
```

### 2. 编程方式使用

```python
from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import DEFAULT_MULTI_PROCESS_CONFIG

# 创建多进程协调器
orchestrator = MultiProcessOrchestrator(DEFAULT_MULTI_PROCESS_CONFIG)

# 执行多进程工作流程
result = orchestrator.main_workflow()
```

### 3. 自定义配置

```python
from tongcheng.config.multi_process_config import MultiProcessConfig
from tongcheng.config.multi_tab_config import MultiTabConfig

# 创建自定义多标签页配置
custom_tab_config = MultiTabConfig(
    max_tabs=4,
    headless=False,
    enable_detailed_logging=True
)

# 创建自定义多进程配置
custom_config = MultiProcessConfig(
    max_processes=3,
    multi_tab_config=custom_tab_config,
    hotel_processing_timeout=1800.0
)

# 使用自定义配置
orchestrator = MultiProcessOrchestrator(custom_config)
```

## 预定义配置模式

### 1. 安全模式（推荐新手）
- **进程数**: 2个
- **每进程标签页数**: 2个
- **特点**: 稳定可靠，资源占用适中
- **适用**: 生产环境，对稳定性要求高

### 2. 默认模式（推荐日常）
- **进程数**: 2个
- **每进程标签页数**: 3个
- **特点**: 平衡性能和稳定性
- **适用**: 大多数使用场景

### 3. 快速模式（追求性能）
- **进程数**: 3个
- **每进程标签页数**: 5个
- **特点**: 最高性能，但可能不够稳定
- **适用**: 对速度要求极高的场景

### 4. 单进程模式（兼容调试）
- **进程数**: 1个
- **每进程标签页数**: 1个
- **特点**: 完全兼容原版，便于调试
- **适用**: 问题排查，兼容性测试

## 系统要求

### 硬件要求

| 模式 | 最低内存 | 推荐内存 | CPU核心 | 网络 |
|------|----------|----------|---------|------|
| 单进程模式 | 2GB | 4GB | 2核 | 稳定 |
| 安全模式 | 4GB | 8GB | 4核 | 稳定 |
| 默认模式 | 6GB | 12GB | 4核 | 稳定 |
| 快速模式 | 8GB | 16GB | 6核+ | 高速稳定 |

### 软件要求
- Python 3.8+
- Chrome/Chromium 浏览器
- 所有原版系统依赖

## 测试和验证

### 运行测试
```bash
# 运行基本功能测试
python tongcheng/test_multi_process.py

# 运行使用示例
python examples/multi_process_example.py
```

### 健康检查
```bash
# 执行系统健康检查
python tongcheng/main_multi_process.py
# 选择 "3. 系统健康检查"
```

## 最佳实践

1. **生产环境部署**
   - 使用安全模式或默认模式
   - 启用详细日志记录
   - 设置资源监控
   - 定期检查系统健康状态

2. **性能优化**
   - 根据网络状况调整并发数
   - 监控系统资源使用情况
   - 定期清理日志文件
   - 优化浏览器配置

3. **错误处理**
   - 设置合理的重试次数
   - 监控错误率
   - 及时处理异常情况
   - 保持系统更新

## 升级指南

### 从单进程版本升级

1. **备份现有配置和数据**
2. **测试多进程版本**
   ```bash
   # 先使用单进程模式测试
   python tongcheng/main_multi_process.py
   # 选择 "1. 单进程模式"
   ```
3. **逐步增加并发**
   - 先使用安全模式
   - 确认稳定后使用默认模式
   - 根据需要选择快速模式

### 配置迁移

原有的配置文件无需修改，多进程版本会自动使用现有配置。

## 技术架构

### 组件结构

```
MultiProcessOrchestrator (多进程协调器)
├── ProcessManager (进程管理器)
│   ├── ProcessMonitor (进程监控器)
│   └── ProcessPoolExecutor (进程池)
├── WorkflowCoordinator (工作流协调器)
├── ErrorHandler (错误处理器)
├── PerformanceMonitor (性能监控器)
└── MultiTabProcessor (多标签页处理器)
    ├── TabManager (标签页管理器)
    └── TongchengPusher (同程推送器)
```

### 处理流程

1. **数据获取阶段** (单进程)
   - 获取飞书酒店数据
   - 获取房型映射关系
   - 获取八大洲价格数据

2. **任务分配阶段**
   - 将酒店分配到不同进程
   - 每个进程内将房型分配到不同标签页

3. **并行处理阶段**
   - 多进程并行处理酒店
   - 每进程内多标签页并行处理房型

4. **结果汇总阶段**
   - 收集所有进程的处理结果
   - 生成统计报告和日志

多进程多标签页版本为同程酒店价格同步系统提供了强大的性能提升，合理配置和使用可以大幅提高工作效率。



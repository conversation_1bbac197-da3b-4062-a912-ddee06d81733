"""
同程酒店价格维护系统 - 多标签页版本
解决多房型处理时的状态冲突问题，大幅提升处理效率
"""

import sys
import time
from typing import Dict, Any
from tongcheng.workflow.workflow import WorkflowCoordinator
from tongcheng.config.multi_tab_config import (
    MultiTabConfig, 
    DEFAULT_CONFIG, 
    FAST_MODE_CONFIG, 
    SAFE_MODE_CONFIG,
    SINGLE_TAB_CONFIG
)
from tongcheng.tools.logger import get_workflow_logger
from tongcheng.tools.error_handler import ErrorHandler
from tongcheng.tools.performance_monitor import PerformanceMonitor

logger = get_workflow_logger()


def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🚀 同程酒店价格维护系统 - 多标签页多进程版本")
    print("=" * 80)
    print("✨ 新特性:")
    print("   • 多标签页并行处理，大幅提升效率")
    print("   • 解决房型处理状态冲突问题")
    print("   • 智能错误恢复和重试机制")
    print("   • 详细的性能监控和日志")
    print("=" * 80)
    print()


def select_processing_mode() -> MultiTabConfig:
    """选择处理模式"""
    print("📋 请选择处理模式:")
    print("1. 单标签页模式 (兼容模式，适合调试)")
    print("2. 安全模式 (2个标签页，稳定可靠)")
    print("3. 默认模式 (3个标签页，平衡性能)")
    print("4. 快速模式 (5个标签页，最高效率)")
    print("5. 自定义模式")
    
    while True:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            print("✅ 选择了单标签页模式")
            return SINGLE_TAB_CONFIG
        elif choice == "2":
            print("✅ 选择了安全模式 (2个标签页)")
            return SAFE_MODE_CONFIG
        elif choice == "3":
            print("✅ 选择了默认模式 (3个标签页)")
            return DEFAULT_CONFIG
        elif choice == "4":
            print("✅ 选择了快速模式 (5个标签页)")
            return FAST_MODE_CONFIG
        elif choice == "5":
            return create_custom_config()
        else:
            print("❌ 无效选择，请重试")


def create_custom_config() -> MultiTabConfig:
    """创建自定义配置"""
    print("\n🔧 自定义配置:")
    
    try:
        max_tabs = int(input("标签页数量 (1-10): "))
        if max_tabs < 1 or max_tabs > 10:
            print("⚠️ 标签页数量超出范围，使用默认值 3")
            max_tabs = 3
            
        room_interval = float(input("房型间处理间隔/秒 (0.5-5.0): "))
        if room_interval < 0.5 or room_interval > 5.0:
            print("⚠️ 间隔时间超出范围，使用默认值 1.0")
            room_interval = 1.0
            
        headless = input("是否使用无头模式? (y/N): ").strip().lower() == 'y'
        
        config = MultiTabConfig(
            max_tabs=max_tabs,
            enable_multi_tab=max_tabs > 1,
            room_interval=room_interval,
            headless=headless
        )
        
        print(f"✅ 自定义配置创建完成:")
        print(f"   标签页数量: {config.max_tabs}")
        print(f"   房型间隔: {config.room_interval}秒")
        print(f"   无头模式: {config.headless}")
        
        return config
        
    except ValueError:
        print("⚠️ 输入格式错误，使用默认配置")
        return DEFAULT_CONFIG


def show_config_details(config: MultiTabConfig):
    """显示配置详情"""
    print(f"\n📊 当前配置详情:")
    print(f"   模式: {'多标签页' if config.enable_multi_tab else '单标签页'}")
    print(f"   标签页数量: {config.max_tabs}")
    print(f"   房型处理间隔: {config.room_interval}秒")
    print(f"   标签页切换延迟: {config.tab_switch_delay}秒")
    print(f"   获取标签页超时: {config.tab_acquire_timeout}秒")
    print(f"   房型处理超时: {config.room_processing_timeout}秒")
    print(f"   最大重试次数: {config.max_retries}")
    print(f"   无头模式: {config.headless}")
    print()


def estimate_performance(config: MultiTabConfig, room_count: int = 4):
    """估算性能提升"""
    single_room_time = 30  # 假设单个房型处理时间
    
    # 单标签页时间
    single_tab_time = room_count * single_room_time
    
    # 多标签页时间
    if config.max_tabs == 1:
        multi_tab_time = single_tab_time
        speedup = 1.0
    else:
        batches = (room_count + config.max_tabs - 1) // config.max_tabs
        multi_tab_time = batches * single_room_time
        speedup = single_tab_time / multi_tab_time
    
    print(f"📈 性能估算 (假设{room_count}个房型):")
    print(f"   单标签页预计时间: {single_tab_time}秒 ({single_tab_time/60:.1f}分钟)")
    print(f"   多标签页预计时间: {multi_tab_time}秒 ({multi_tab_time/60:.1f}分钟)")
    print(f"   预计加速比: {speedup:.1f}x")
    print(f"   预计节省时间: {single_tab_time - multi_tab_time}秒")
    print()


def run_workflow(config: MultiTabConfig) -> bool:
    """运行工作流程"""
    logger.info("开始初始化工作流程...")
    
    # 初始化错误处理器和性能监控器
    error_handler = ErrorHandler()
    performance_monitor = PerformanceMonitor()
    
    # 创建工作流协调器
    coordinator = WorkflowCoordinator(
        error_handler=error_handler,
        performance_monitor=performance_monitor,
        use_smart_maintenance=True,  # 启用智能维护
        multi_tab_config=config
    )
    
    try:
        print("🔗 正在建立连接...")
        start_time = time.time()
        
        # 执行主工作流程
        result = coordinator.execute_main_workflow()
        
        total_time = time.time() - start_time
        
        # 显示结果
        if result.get("status") == "success":
            print(f"\n🎉 工作流程执行成功!")
            print(f"   总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
            
            # 显示性能统计
            if hasattr(performance_monitor, 'get_stats'):
                stats = performance_monitor.get_stats()
                print(f"   处理统计: {stats}")
            
            return True
        else:
            print(f"\n❌ 工作流程执行失败: {result.get('message', '未知错误')}")
            return False
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断执行")
        return False
    except Exception as e:
        print(f"\n❌ 执行过程中发生异常: {str(e)}")
        logger.error(f"工作流程执行异常: {str(e)}")
        return False
    finally:
        # 显示错误统计
        if hasattr(error_handler, 'get_error_summary'):
            error_summary = error_handler.get_error_summary()
            if error_summary:
                print(f"\n📋 错误统计: {error_summary}")


def main():
    """主函数"""
    print_banner()
    
    try:
        # 选择处理模式
        config = select_processing_mode()
        
        # 显示配置详情
        show_config_details(config)
        
        # 性能估算
        estimate_performance(config)
        
        # 确认执行
        if config.enable_multi_tab:
            print("⚠️ 多标签页模式将启动多个浏览器标签页")
            print("   请确保系统有足够的内存和CPU资源")
        
        confirm = input("确认开始执行? (y/N): ").strip().lower()
        if confirm != 'y':
            print("👋 已取消执行")
            return
        
        print(f"\n🚀 开始执行工作流程...")
        print(f"   模式: {'多标签页' if config.enable_multi_tab else '单标签页'}")
        print(f"   标签页数量: {config.max_tabs}")
        print()
        
        # 运行工作流程
        success = run_workflow(config)
        
        if success:
            print(f"\n✅ 所有任务执行完成!")
        else:
            print(f"\n⚠️ 任务执行过程中出现问题，请检查日志")
            
    except KeyboardInterrupt:
        print(f"\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        logger.error(f"主程序异常: {str(e)}")
    
    print(f"\n程序结束")


if __name__ == "__main__":
    main()

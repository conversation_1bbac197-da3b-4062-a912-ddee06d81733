"""
多标签页配置管理
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class MultiTabConfig:
    """多标签页配置"""
    
    # 基本设置
    max_tabs: int = 3  # 最大标签页数量
    enable_multi_tab: bool = True  # 是否启用多标签页模式
    
    # 超时设置
    tab_acquire_timeout: float = 30.0  # 获取标签页超时时间（秒）
    room_processing_timeout: float = 300.0  # 单个房型处理超时时间（秒）
    
    # 重试设置
    max_retries: int = 2  # 最大重试次数
    retry_delay: float = 5.0  # 重试延迟（秒）
    
    # 性能设置
    room_interval: float = 1.0  # 房型间处理间隔（秒）
    tab_switch_delay: float = 2.0  # 标签页切换延迟（秒）
    
    # 浏览器设置
    headless: bool = False  # 是否无头模式
    cookies_path: str = "cookies.json"  # Cookie文件路径
    
    # 日志设置
    enable_detailed_logging: bool = True  # 是否启用详细日志
    log_performance_metrics: bool = True  # 是否记录性能指标
    
    @classmethod
    def create_default(cls) -> 'MultiTabConfig':
        """创建默认配置"""
        return cls()
    
    @classmethod
    def create_fast_mode(cls) -> 'MultiTabConfig':
        """创建快速模式配置（更多标签页，更短间隔）"""
        return cls(
            max_tabs=5,
            room_interval=0.5,
            tab_switch_delay=1.0,
            tab_acquire_timeout=15.0
        )
    
    @classmethod
    def create_safe_mode(cls) -> 'MultiTabConfig':
        """创建安全模式配置（较少标签页，较长间隔）"""
        return cls(
            max_tabs=2,
            room_interval=2.0,
            tab_switch_delay=3.0,
            tab_acquire_timeout=60.0,
            room_processing_timeout=600.0
        )
    
    @classmethod
    def create_single_tab_mode(cls) -> 'MultiTabConfig':
        """创建单标签页模式配置"""
        return cls(
            max_tabs=1,
            enable_multi_tab=False,
            room_interval=1.5
        )
    
    @classmethod
    def create_multi_process_mode(cls) -> 'MultiTabConfig':
        """创建多进程专用配置（每个进程使用较少标签页）"""
        return cls(
            max_tabs=2,
            enable_multi_tab=True,
            room_interval=1.5,
            tab_switch_delay=2.5,
            tab_acquire_timeout=45.0,
            room_processing_timeout=450.0,
            max_retries=2,
            enable_detailed_logging=True
        )
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.max_tabs < 1:
            return False
        if self.tab_acquire_timeout <= 0:
            return False
        if self.room_processing_timeout <= 0:
            return False
        if self.max_retries < 0:
            return False
        if self.retry_delay < 0:
            return False
        if self.room_interval < 0:
            return False
        if self.tab_switch_delay < 0:
            return False
        return True
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'max_tabs': self.max_tabs,
            'enable_multi_tab': self.enable_multi_tab,
            'tab_acquire_timeout': self.tab_acquire_timeout,
            'room_processing_timeout': self.room_processing_timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'room_interval': self.room_interval,
            'tab_switch_delay': self.tab_switch_delay,
            'headless': self.headless,
            'cookies_path': self.cookies_path,
            'enable_detailed_logging': self.enable_detailed_logging,
            'log_performance_metrics': self.log_performance_metrics
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'MultiTabConfig':
        """从字典创建配置"""
        return cls(**data)


# 预定义配置
DEFAULT_CONFIG = MultiTabConfig.create_default()
FAST_MODE_CONFIG = MultiTabConfig.create_fast_mode()
SAFE_MODE_CONFIG = MultiTabConfig.create_safe_mode()
SINGLE_TAB_CONFIG = MultiTabConfig.create_single_tab_mode()

# 多进程专用配置
MULTI_PROCESS_TAB_CONFIG = MultiTabConfig.create_multi_process_mode()

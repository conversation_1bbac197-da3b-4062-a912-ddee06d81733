#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能价格维护配置文件
管理智能维护功能的各种参数和设置
"""

import datetime
from typing import Dict, Any

class SmartMaintenanceConfig:
    """智能价格维护配置类"""
    
    def __init__(self):
        # 基本配置
        self.enabled = True  # 是否启用智能维护
        self.fallback_to_traditional = True  # 失败时是否回退到传统方法
        
        # 价格比较配置
        self.price_threshold = 0.05  # 价格差异阈值（5%）
        self.currency_rate = 0.91    # 人民币转港币汇率
        
        # 翻页配置
        self.max_pages = 3           # 最大翻页数
        self.dates_per_page = 14     # 每页日期数
        self.page_load_timeout = 5   # 页面加载超时（秒）
        
        # 时间窗口配置
        self.midnight_window_start = 0   # 零点后时间窗口开始（小时）
        self.midnight_window_end = 7     # 零点后时间窗口结束（小时）
        self.enable_midnight_detection = True  # 是否启用时间窗口检测
        
        # 日志配置
        self.detailed_logging = True     # 是否显示详细日志
        self.log_date_parsing = True     # 是否记录日期解析过程
        self.log_efficiency_stats = True # 是否显示效率统计
        
        # 性能配置
        self.batch_processing = True     # 是否启用批量处理
        self.skip_unchanged_cells = True # 是否跳过无变化的格子
        self.validate_date_sequence = True # 是否验证日期序列连续性
        
        # 重试配置
        self.max_retries = 3            # 最大重试次数
        self.retry_delay = 2            # 重试延迟（秒）
        
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            'enabled': self.enabled,
            'fallback_to_traditional': self.fallback_to_traditional,
            'price_threshold': self.price_threshold,
            'currency_rate': self.currency_rate,
            'max_pages': self.max_pages,
            'dates_per_page': self.dates_per_page,
            'page_load_timeout': self.page_load_timeout,
            'midnight_window_start': self.midnight_window_start,
            'midnight_window_end': self.midnight_window_end,
            'enable_midnight_detection': self.enable_midnight_detection,
            'detailed_logging': self.detailed_logging,
            'log_date_parsing': self.log_date_parsing,
            'log_efficiency_stats': self.log_efficiency_stats,
            'batch_processing': self.batch_processing,
            'skip_unchanged_cells': self.skip_unchanged_cells,
            'validate_date_sequence': self.validate_date_sequence,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
        }
    
    def is_midnight_window(self) -> bool:
        """检查当前是否处于零点后时间窗口"""
        if not self.enable_midnight_detection:
            return False
        
        current_hour = datetime.datetime.now().hour
        return self.midnight_window_start <= current_hour <= self.midnight_window_end
    
    def print_config(self):
        """打印当前配置"""
        print("=== 智能价格维护配置 ===")
        print(f"🚀 智能维护: {'启用' if self.enabled else '禁用'}")
        print(f"🔄 自动回退: {'启用' if self.fallback_to_traditional else '禁用'}")
        print()
        
        print("📊 价格比较配置:")
        print(f"  价格阈值: {self.price_threshold * 100}%")
        print(f"  汇率: {self.currency_rate}")
        print()
        
        print("📖 翻页配置:")
        print(f"  最大页数: {self.max_pages}")
        print(f"  每页日期数: {self.dates_per_page}")
        print(f"  页面超时: {self.page_load_timeout}秒")
        print()
        
        print("🕛 时间窗口配置:")
        print(f"  检测启用: {'是' if self.enable_midnight_detection else '否'}")
        print(f"  窗口时间: {self.midnight_window_start}:00 - {self.midnight_window_end}:00")
        print(f"  当前状态: {'时间窗口内' if self.is_midnight_window() else '正常时段'}")
        print()
        
        print("📝 日志配置:")
        print(f"  详细日志: {'启用' if self.detailed_logging else '禁用'}")
        print(f"  日期解析日志: {'启用' if self.log_date_parsing else '禁用'}")
        print(f"  效率统计: {'启用' if self.log_efficiency_stats else '禁用'}")
        print()
        
        print("⚡ 性能配置:")
        print(f"  批量处理: {'启用' if self.batch_processing else '禁用'}")
        print(f"  跳过无变化格子: {'启用' if self.skip_unchanged_cells else '禁用'}")
        print(f"  日期序列验证: {'启用' if self.validate_date_sequence else '禁用'}")
        print()

# 预定义配置模板
class ConfigTemplates:
    """配置模板"""
    
    @staticmethod
    def get_production_config() -> SmartMaintenanceConfig:
        """生产环境配置"""
        config = SmartMaintenanceConfig()
        config.enabled = True
        config.fallback_to_traditional = True
        config.detailed_logging = False  # 生产环境减少日志
        config.log_date_parsing = False
        config.max_retries = 5  # 生产环境增加重试次数
        return config
    
    @staticmethod
    def get_development_config() -> SmartMaintenanceConfig:
        """开发环境配置"""
        config = SmartMaintenanceConfig()
        config.enabled = True
        config.fallback_to_traditional = True
        config.detailed_logging = True  # 开发环境显示详细日志
        config.log_date_parsing = True
        config.log_efficiency_stats = True
        config.max_retries = 2  # 开发环境快速失败
        return config
    
    @staticmethod
    def get_testing_config() -> SmartMaintenanceConfig:
        """测试环境配置"""
        config = SmartMaintenanceConfig()
        config.enabled = True
        config.fallback_to_traditional = False  # 测试环境不回退，便于发现问题
        config.detailed_logging = True
        config.log_date_parsing = True
        config.log_efficiency_stats = True
        config.validate_date_sequence = True
        config.max_retries = 1  # 测试环境不重试
        return config
    
    @staticmethod
    def get_conservative_config() -> SmartMaintenanceConfig:
        """保守配置（兼容性优先）"""
        config = SmartMaintenanceConfig()
        config.enabled = False  # 禁用智能维护，使用传统方法
        config.fallback_to_traditional = True
        config.detailed_logging = False
        return config
    
    @staticmethod
    def get_aggressive_config() -> SmartMaintenanceConfig:
        """激进配置（性能优先）"""
        config = SmartMaintenanceConfig()
        config.enabled = True
        config.fallback_to_traditional = False  # 不回退，强制使用智能维护
        config.price_threshold = 0.02  # 更严格的价格阈值
        config.batch_processing = True
        config.skip_unchanged_cells = True
        config.detailed_logging = False  # 减少日志开销
        config.max_retries = 1  # 快速失败
        return config

# 全局配置实例
default_config = SmartMaintenanceConfig()

def get_smart_maintenance_config(env: str = "development") -> SmartMaintenanceConfig:
    """
    根据环境获取智能维护配置
    :param env: 环境名称 (production, development, testing, conservative, aggressive)
    :return: 配置实例
    """
    if env == "production":
        return ConfigTemplates.get_production_config()
    elif env == "development":
        return ConfigTemplates.get_development_config()
    elif env == "testing":
        return ConfigTemplates.get_testing_config()
    elif env == "conservative":
        return ConfigTemplates.get_conservative_config()
    elif env == "aggressive":
        return ConfigTemplates.get_aggressive_config()
    else:
        return default_config

def demo_configs():
    """演示不同配置"""
    
    environments = ["production", "development", "testing", "conservative", "aggressive"]
    
    for env in environments:
        print(f"\n{'='*20} {env.upper()} 配置 {'='*20}")
        config = get_smart_maintenance_config(env)
        config.print_config()

if __name__ == "__main__":
    demo_configs()

from dataclasses import dataclass, field
from typing import Optional
from .multi_tab_config import MultiTabConfig


@dataclass
class MultiProcessConfig:
    """多进程配置"""

    # 进程设置
    max_processes: int = 2  # 最大进程数量
    enable_multi_process: bool = True  # 是否启用多进程模式

    # 每个进程的多标签页配置
    multi_tab_config: Optional[MultiTabConfig] = None  # 多标签页配置

    # 超时设置
    process_startup_timeout: float = 60.0  # 进程启动超时时间（秒）
    process_shutdown_timeout: float = 30.0  # 进程关闭超时时间（秒）
    hotel_processing_timeout: float = 1800.0  # 单个酒店处理超时时间（秒）

    # 重试设置
    max_process_retries: int = 1  # 进程级别最大重试次数
    process_retry_delay: float = 10.0  # 进程重试延迟（秒）

    # 性能设置
    hotel_interval: float = 2.0  # 酒店间处理间隔（秒）
    process_start_delay: float = 5.0  # 进程启动间隔（秒）

    # 资源管理
    memory_limit_mb: Optional[int] = None  # 内存限制（MB）
    cpu_limit_percent: Optional[float] = None  # CPU使用限制（百分比）

    # 日志设置
    enable_process_logging: bool = True  # 是否启用进程日志
    log_process_metrics: bool = True  # 是否记录进程指标

    def __post_init__(self):
        """初始化后处理"""
        if self.multi_tab_config is None:
            # 默认使用安全模式的多标签页配置
            from .multi_tab_config import SAFE_MODE_CONFIG
            self.multi_tab_config = SAFE_MODE_CONFIG

    @classmethod
    def create_default(cls) -> 'MultiProcessConfig':
        """创建默认配置"""
        from .multi_tab_config import DEFAULT_CONFIG
        return cls(multi_tab_config=DEFAULT_CONFIG)

    @classmethod
    def create_safe_mode(cls) -> 'MultiProcessConfig':
        """创建安全模式配置"""
        from .multi_tab_config import SAFE_MODE_CONFIG
        return cls(
            max_processes=2,
            multi_tab_config=SAFE_MODE_CONFIG,
            process_startup_timeout=90.0,
            hotel_processing_timeout=2400.0
        )

    @classmethod
    def create_fast_mode(cls) -> 'MultiProcessConfig':
        """创建快速模式配置"""
        from .multi_tab_config import FAST_MODE_CONFIG
        return cls(
            max_processes=3,
            multi_tab_config=FAST_MODE_CONFIG,
            process_startup_timeout=45.0,
            hotel_processing_timeout=1200.0,
            hotel_interval=1.0,
            process_start_delay=3.0
        )

    @classmethod
    def create_single_process_mode(cls) -> 'MultiProcessConfig':
        """创建单进程模式配置（兼容模式）"""
        from .multi_tab_config import SINGLE_TAB_CONFIG
        return cls(
            max_processes=1,
            enable_multi_process=False,
            multi_tab_config=SINGLE_TAB_CONFIG
        )


# 预定义配置
DEFAULT_MULTI_PROCESS_CONFIG = MultiProcessConfig.create_default()
SAFE_MULTI_PROCESS_CONFIG = MultiProcessConfig.create_safe_mode()
FAST_MULTI_PROCESS_CONFIG = MultiProcessConfig.create_fast_mode()
SINGLE_PROCESS_CONFIG = MultiProcessConfig.create_single_process_mode()

import requests
import json
from datetime import datetime, timedelta
from tongcheng.fetcher.base_fetcher import BaseFetcher
from tongcheng.config_manager import get_badazhou_config
from tongcheng.tools.logger import get_fetcher_logger

# 使用统一日志管理器
logger = get_fetcher_logger()

class BadazhouFetcher(BaseFetcher):
    """
    八大洲渠道数据抓取实现，支持批量 hotelCode 和日期区间查询。
    """
    
    def __init__(self):
        """初始化八大洲数据抓取器"""
        self.logger = logger
        
    def fetch_price(self, hotelCode, checkInDate, checkOutDate):
        """
        单次获取酒店价格信息。
        """
        config = get_badazhou_config()
        payload = json.dumps({
            "hotelCode": hotelCode,
            "checkInDate": checkInDate,
            "checkOutDate": checkOutDate,
            "personCount": config.get("personCount"),
            "roomCount": config.get("roomCount"),
            "channel": config.get("channel")
        })
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("GET", "http://139.199.168.35:8080/getPrice", headers=headers, data=payload)
        try:
            resp_json = json.loads(response.text)
            data = resp_json.get('data', {})
            result = {
                "hotelCode": data.get("hotelCode", ""),
                "hotelName": data.get("hotelName", ""),
                "rooms": []
            }
            for room in data.get("roomList", []):
                room_info = {
                    "roomName": room.get("roomName", ""),
                    "roomCode": room.get("roomCode", ""),
                    "averagePriceList": []
                }
                price_list = room.get("priceList", [])
                if price_list:
                    avg_price_list = price_list[0].get("averagePriceList", [])
                    room_info["averagePriceList"] = avg_price_list
                result["rooms"].append(room_info)
            return result
        except Exception as e:
            return {"error": f"解析响应数据出错: {e}"}

    def fetch_price_range(self, hotelCodes, start_date, days):
        """
        批量获取多个 hotelCode 在指定日期区间内的价格信息。
        :param hotelCodes: list[str]，酒店编码列表
        :param start_date: str，起始日期，格式 YYYY-MM-DD
        :param days: int，查询天数
        :return: dict，结构为 {hotelCode: [每日结果, ...], ...}
        """
        self.logger.info(f"开始批量获取价格数据 - 酒店数量: {len(hotelCodes)}, 起始日期: {start_date}, 查询天数: {days}")
        
        results = {}
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        
        for code in hotelCodes:
            self.logger.info(f"获取酒店 {code} 的价格数据")
            code_results = []
            
            for i in range(days):
                check_in = (start_dt + timedelta(days=i)).strftime("%Y-%m-%d")
                check_out = (start_dt + timedelta(days=i+1)).strftime("%Y-%m-%d")
                
                try:
                    res = self.fetch_price(code, check_in, check_out)
                    # 结果中补充日期信息，便于追溯
                    if isinstance(res, dict):
                        res["checkInDate"] = check_in
                        res["checkOutDate"] = check_out
                    code_results.append(res)
                except Exception as e:
                    self.logger.error(f"获取酒店 {code} 日期 {check_in} 价格数据失败: {str(e)}")
                    code_results.append({"error": str(e), "checkInDate": check_in, "checkOutDate": check_out})

            results[code] = code_results
            self.logger.info(f"酒店 {code} 价格数据获取完成，获取 {len(code_results)} 天数据")

        self.logger.info(f"批量价格数据获取完成，处理了 {len(hotelCodes)} 个酒店")
        return results

    def fetch_price_sliding_window(self, hotelCode, start_date, days, target_room_name):
        """
        滑动窗口三晚连住查价：每次checkIn=当天，checkOut=当天+3天，窗口每天滑动，返回每一天的三晚连住价（只保留checkIn当天的价格）。
        :param hotelCode: 酒店编码
        :param start_date: 起始日期，格式YYYY-MM-DD
        :param days: 查询天数
        :param target_room_name: 目标房型名称
        :return: dict {date: price}
        """
        result = {}
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        for i in range(days - 2):  # 最后两天无法再查三晚
            check_in = start_dt + timedelta(days=i)
            check_out = check_in + timedelta(days=3)
            res = self.fetch_price(hotelCode, check_in.strftime('%Y-%m-%d'), check_out.strftime('%Y-%m-%d'))
            try:
                if not isinstance(res, dict):
                    continue
                room_list = res.get("rooms", [])
                target_room = None
                for room in room_list:
                    if room.get("roomName") == target_room_name:
                        target_room = room
                        break
                if not target_room:
                    continue
                price_list = target_room.get("averagePriceList", [])
                if price_list:
                    for day_price in price_list:
                        for date_str, price in day_price.items():
                            if date_str == check_in.strftime('%Y-%m-%d'):
                                result[date_str] = float(price)
            except Exception as e:
                self.logger.error(f"滑动窗口查价解析异常: {e}")
        return result

    def fetch_price_range_sliding_window(self, hotelCodes, start_date, days):
        """
        批量获取多个 hotelCode 在指定日期区间内的所有房型三晚连住价格信息。
        :param hotelCodes: list[str]，酒店编码列表
        :param start_date: str，起始日期，格式 YYYY-MM-DD
        :param days: int，查询天数
        :return: dict，结构为 {hotelCode: [每日结果, ...], ...}
        """
        self.logger.info(f"开始批量获取三晚连住价格数据 - 酒店数量: {len(hotelCodes)}, 起始日期: {start_date}, 查询天数: {days}")
        results = {}
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        for code in hotelCodes:
            self.logger.info(f"获取酒店 {code} 的三晚连住价格数据")
            code_results = []
            for i in range(days - 2):  # 最后两天无法再查三晚
                check_in = (start_dt + timedelta(days=i)).strftime("%Y-%m-%d")
                check_out = (start_dt + timedelta(days=i+3)).strftime("%Y-%m-%d")
                res = self.fetch_price(code, check_in, check_out)
                try:
                    if not isinstance(res, dict):
                        continue
                    code_results.append({
                        "checkInDate": check_in,
                        "checkOutDate": check_out,
                        "rooms": res.get("rooms", [])
                    })
                except Exception as e:
                    self.logger.error(f"三晚连住查价解析异常: {e}")
                    code_results.append({"error": str(e), "checkInDate": check_in, "checkOutDate": check_out})
            results[code] = code_results
            self.logger.info(f"酒店 {code} 三晚连住价格数据获取完成，获取 {len(code_results)} 天数据")
        self.logger.info(f"批量三晚连住价格数据获取完成，处理了 {len(hotelCodes)} 个酒店")
        return results




if __name__ == "__main__":
    fetcher = BadazhouFetcher()
    result = fetcher.fetch_price("kvqwsi452900", "2025-07-01", "2025-07-02")
    print(result)

# 输出样例
'''
    {'hotelCode': 'kvqwsi452900', 'hotelName': '新加坡费尔蒙酒店', 'rooms': [
        {'roomName': '大使套房（南楼+北楼）(双床)', 'roomCode': '198872', 'averagePriceList': [{'2025-07-01': '8884.0'}]},
        {'roomName': '大使套房（南楼+北楼）(大床)', 'roomCode': '198871', 'averagePriceList': [{'2025-07-01': '8884.0'}]},
        {'roomName': '特色套房（北楼）(大床)', 'roomCode': '198821', 'averagePriceList': [{'2025-07-01': '3226.0'}]},
        {'roomName': '特色沙龙套房（南楼）(大床)', 'roomCode': '198861', 'averagePriceList': [{'2025-07-01': '3546.0'}]},
        {'roomName': '豪华房（南楼）(大床)', 'roomCode': '198831', 'averagePriceList': [{'2025-07-01': '3021.0'}]},
        {'roomName': '豪华港景客房（南楼）(大床)', 'roomCode': '198841', 'averagePriceList': [{'2025-07-01': '3342.0'}]},
        {'roomName': '费尔蒙金尊客房（南楼）(大床)', 'roomCode': '198781',
         'averagePriceList': [{'2025-07-01': '3692.0'}]},
        {'roomName': '费尔蒙金尊港景客房（南楼）(大床)', 'roomCode': '198791',
         'averagePriceList': [{'2025-07-01': '4042.0'}]}]}
'''


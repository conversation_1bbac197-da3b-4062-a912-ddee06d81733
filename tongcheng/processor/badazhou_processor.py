from tongcheng.models.hotel_models import Hotel, RoomType
from tongcheng.models.price_models import PriceCalendar
from datetime import datetime, date

class BadazhouProcessor:
    """
    八大洲数据标准化处理器，将原始数据转为标准模型。
    """
    def process(self, raw_data):
        """
        :param raw_data: fetcher 返回的原始数据（dict）
        :return: Hotel 对象
        """
        hotel = Hotel(
            id=raw_data.get("hotelCode", ""),
            name=raw_data.get("hotelName", ""),
            room_types=[]
        )
        for room in raw_data.get("rooms", []):
            average_price_list = []
            for price_info in room.get("averagePriceList", []):
                # averagePriceList 是 [{"2025-07-01": "8884.0"}] 结构
                for d_str, p_str in price_info.items():
                    try:
                        d = date.fromisoformat(d_str)
                        p = float(p_str)
                        price_calendar = PriceCalendar(date=d, base_price=p)
                        average_price_list.append(price_calendar)
                    except Exception as e:
                        # 可加日志或备用方案
                        continue
            room_type = RoomType(
                id=room.get("roomCode", ""),
                name=room.get("roomName", ""),
                average_price_list=average_price_list
            )
            hotel.room_types.append(room_type)
        return hotel


if __name__ == "__main__":
    from tongcheng.fetcher.badazhou_fetcher import BadazhouFetcher
    fetcher = BadazhouFetcher()
    processor = BadazhouProcessor()
    # 获取5天的数据
    hotel_codes = ["kvqwsi452900"]
    results = fetcher.fetch_price_range(hotel_codes, "2025-07-01", 5)
    
    # 处理每个酒店的数据
    for hotel_code, daily_results in results.items():
        print(f"\n===== 酒店 {hotel_code} 的5天数据 =====")
        for daily_result in daily_results:
            hotel = processor.process(daily_result)
            print(f"\n日期: {daily_result['checkInDate']} -> {daily_result['checkOutDate']}")
            print(f"酒店ID: {hotel.id}")
            print(f"酒店名称: {hotel.name}")
            print("\n房型信息:")
            for room_type in hotel.room_types:
                print(f"\n房型ID: {room_type.id}")
                print(f"房型名称: {room_type.name}")
                print("价格日历:")
                for price in room_type.average_price_list:
                    print(f"  日期: {price.date}, 价格: {price.base_price}")

class TongchengRoomOperator:
    def __init__(self, page, logger):
        self.page = page
        self.logger = logger

    def select_room_type(self, room_name: str) -> bool:
        """选择指定房型"""
        try:
            room_type_sele = 'x://*[@id="product-app"]/div/div[3]/div[1]/form/div[1]/div/div/div[1]'
            self.page.ele(room_type_sele).click()
            li_elements = self.page.eles("x://body/div[@class='el-select-dropdown el-popper is-multiple']/div[@class='el-scrollbar']/div[@class='el-select-dropdown__wrap el-scrollbar__wrap']/ul[@class='el-scrollbar__view el-select-dropdown__list']/li")
            for i, li_ele in enumerate(li_elements):
                room_name_option = li_ele.ele('x:/div/div').text
                if room_name == room_name_option:
                    li_ele.click()
                    self.page.ele("x://div[@class='additionBtns flex items-center justify-center']/div[2]").click()  # 点击确定
                    return True
            self.logger.error(f"未找到房型: {room_name}")
            return False
        except Exception as e:
            self.logger.error(f"选择房型失败: {str(e)}")
            return False 
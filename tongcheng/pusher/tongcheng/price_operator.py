import time
import datetime

from DrissionPage._functions.keys import Keys
from DrissionPage.errors import NoRectError  # 确保已导入异常


class TongchengPriceOperator:
    def __init__(self, page, logger, currency_converter, hotel_name=None, click_statistics=None):
        self.tongcheng_hotel_room_name = None
        self.page = page
        self.logger = logger
        self.currency_converter = currency_converter
        self.start_index = None  # "今日"所在th的列号
        self.hotel_name = hotel_name  # 新增：酒店名称
        self.click_statistics = click_statistics  # 新增：点击统计对象



    def fill_price(self, price_data: dict, room_name) -> bool | None:
        """
        填充价格数据，优先使用智能维护新逻辑（只维护有变化的格子，支持房型选择、日期智能解析等），
        如新逻辑失败则自动回退到旧逻辑。对外接口不变。
        :param room_name: 同程房型名称
        :param price_data: {date_str: price}
        :return: 是否推送成功
        """
        # ========== 新增：智能维护新逻辑优先 ========== #
        try:
            self.tongcheng_hotel_room_name = room_name
            # 2. 生成目标价格矩阵（斜三角三晚连住+关房）
            matrix, date_list = self._generate_price_matrix(price_data)
            self.logger.info("=== 要填入的价格矩阵（人民币） ===")
            for i, row in enumerate(matrix):
                self.logger.info(f"第{i+1}行: {' '.join([str(x) for x in row])}")
            self.logger.info("=== 价格矩阵打印结束 ===")
            # 3. 打印港币价格矩阵
            matrix_hkd = [
                [x if x in (None, 9999) else self.currency_converter.cny_to_hkd(x) for x in row]
                for row in matrix
            ]
            self.logger.info("=== 要填入的价格矩阵（港币） ===")
            for i, row in enumerate(matrix_hkd):
                self.logger.info(f"第{i+1}行: {' '.join([str(x) for x in row])}")
            self.logger.info("=== 港币价格矩阵打印结束 ===")
            # 4. 采集实际页面价格矩阵（支持房型选择、翻页、日期智能解析）
            actual_matrix = self.get_actual_price_matrix(date_list, room_name)
            # 5. 智能比对并只维护有变化的格子
            self.compare_and_fill_matrix(matrix, actual_matrix, date_list)
            # 6. 回到首页
            self.back_to_first_page()
            self.logger.info('[智能维护] 智能维护流程完成，已回到首页')
            return True
        except Exception as e:
            self.logger.warning(f"[智能维护] 发生异常，切换到旧逻辑... 错误信息: {e}")
            self.logger.error(f"[智能维护] 发生异常，切换到旧逻辑: {str(e)}")
            # ========== 新逻辑失败，自动回退到旧逻辑 ========== #
            try:
                # 旧逻辑：原有流程（与新逻辑类似，但不保证只维护有变化的格子）
                self.get_today_col_index()
                self.tongcheng_hotel_room_name = room_name
                matrix, date_list = self._generate_price_matrix(price_data)
                self.logger.info("=== 要填入的价格矩阵（人民币） ===")
                for i, row in enumerate(matrix):
                    self.logger.info(f"第{i+1}行: {' '.join([str(x) for x in row])}")
                self.logger.info("=== 价格矩阵打印结束 ===")
                matrix_hkd = [
                    [x if x in (None, 9999) else self.currency_converter.cny_to_hkd(x) for x in row]
                    for row in matrix
                ]
                self.logger.info("=== 要填入的价格矩阵（港币） ===")
                for i, row in enumerate(matrix_hkd):
                    self.logger.info(f"第{i+1}行: {' '.join([str(x) for x in row])}")
                self.logger.info("=== 港币价格矩阵打印结束 ===")
                n = len(date_list)
                rows = 4
                actual_matrix = self.get_actual_price_matrix(date_list, room_name)
                self.compare_and_fill_matrix(matrix, actual_matrix, date_list)
                self.back_to_first_page()
                self.logger.info('[旧逻辑] 旧逻辑流程完成，已回到首页')
                return True
            except Exception as e2:
                self.logger.error(f"[旧逻辑] 价格推送失败: {e2}")
                self.logger.error(f"[旧逻辑] 价格推送失败: {str(e2)}")
                return False

    def back_to_first_page(self):
        self.page.ele('x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[1]').click()

    def compare_and_fill_matrix(self, matrix, actual_matrix, date_list):
        """
        比对目标矩阵和实际矩阵，仅对不一致且不在5%以内的格子进行修改。
        目标为None/9999且实际为9999/None则无需操作，目标为None/9999且实际为数字则设置为无效。
        比较价格时，目标价格先换算为港币。
        日志中打印原始目标价格、换算后港币价格、实际格子价格、最终要填入的价格。
        修正：比对时用col=page_start+(page_col-1)作为全局目标矩阵的索引，td_col第一页和后续页不同。
        新增：严格对齐输出所有需要被修改的格子。
        """
        n = len(date_list)
        rows = 4
        page_start = 0
        page_idx = 0
        need_modify_cells = []  # 新增：收集所有需要被修改的格子
        while page_start < n:
            if page_idx == 0:
                grid_count = min(15 - self.start_index + 1, n - page_start)
            else:
                grid_count = min(14, n - page_start)
            self.logger.info("=== 实际采集到的价格矩阵 ===")
            for row in range(rows):
                row_prices = []
                for col in range(n):
                    if col < len(actual_matrix[row]):
                        val = actual_matrix[row][col]
                        row_prices.append(str(val))
                    else:
                        row_prices.append("-")
                self.logger.info(f"第{row+1}行: {' '.join(row_prices)}")
            self.logger.info("=== 实际矩阵打印结束 ===")
            for row in range(rows):
                for page_col in range(grid_count):
                    col = page_start + page_col
                    if col >= n:
                        continue
                    target_val = matrix[row][col]
                    actual_val = actual_matrix[row][col]  # 全局列号对齐
                    date_str = date_list[col]
                    if target_val is None or str(target_val) == '9999':
                        if actual_val is None or str(actual_val) == '9999':
                            continue
                        else:
                            # 需设无效
                            need_modify_cells.append({
                                'row': row+1,
                                'col': col+1,
                                'date': date_str,
                                'target': target_val,
                                'actual': actual_val,
                                'final': target_val
                            })
                            self.logger.info(f"[需设无效] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），目标: {target_val}，实际: {actual_val}")
                            self.fill_single_cell(row, page_col, col+1, target_val, date_str, page_idx)

                        continue
                    target_val_hkd = self.currency_cny_to_hkd(target_val)
                    if str(target_val_hkd) != str(actual_val) and not (str(target_val_hkd) == '9999' and str(actual_val) == '9999'):
                        if self.is_price_close(target_val_hkd, actual_val):
                            # 新增：价格相差在5%以内但无需修改的日志
                            self.logger.debug(f"[无需修改] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），目标: {target_val_hkd}，实际: {actual_val}，差值在5%以内，无需修改")
                            continue
                        # 需修改
                        need_modify_cells.append({
                            'row': row+1,
                            'col': col+1,
                            'date': date_str,
                            'target': target_val,
                            'actual': actual_val,
                            'final': target_val_hkd
                        })
                        self.logger.info(f"[需修改] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），原始目标: {target_val}，换算港币: {target_val_hkd}，实际: {actual_val}，最终填入: {target_val_hkd}")
                        self.fill_single_cell(row, page_col, col+1, target_val, date_str, page_idx)

            page_start += grid_count
            page_idx += 1
            if page_start < n:
                self.click_next_two_weeks()
                time.sleep(2)
        # 新增：统一输出所有需要被修改的格子
        self.logger.info("==============================")
        if need_modify_cells:
            self.logger.info("需要修改的格子如下：")
            for cell in need_modify_cells:
                self.logger.info(f"第{cell['row']}行，第{cell['col']}列（{cell['date']}），目标: {cell['target']}，实际: {cell['actual']}，最终填入: {cell['final']}")
        else:
            self.logger.info("所有格子均已匹配，无需修改")
        self.logger.info("==============================")
        self.logger.info("=== 自动化页面填充完成 ===")

    def is_price_close(self, target, actual, threshold=0.05):
        """
        判断两个价格相差是否在阈值百分比以内。
        :param target: 目标价格
        :param actual: 实际价格
        :param threshold: 百分比阈值，默认5%
        :return: True表示相差在阈值内
        """
        try:
            target = int(target)
            actual = int(actual)
            if target == 0:
                return actual == 0
            return abs(target - actual) / abs(target) <= threshold
        except Exception:
            return False
    def fill_single_cell(self, row, page_col, cell_col, real_price, date_str, page_idx=0):
        """
        负责单元格的点击、输入、保存、关闭弹窗、设置无效等
        :param page_idx: 当前页索引，0为第一页
        """
        cell_start = time.time()
        self.logger.info(f"第{row+1}行，第{cell_col}格（本页第{page_col}格），日期{date_str}，填入价格: {real_price}")
        td_col = 2 + page_col  # 每一页都从2开始递增
        price_box_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]'
        price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
        if not price_box:
            # self.logger.warning(f"未找到价格框 {date_str}")
            return
        price_box.click()
        if real_price is None or real_price == 9999:
            self.logger.info(f"{date_str} 目标为None或9999，直接设置为无效")
            self.click_set_price_invalid()
            return
        real_price_hkd = self.currency_cny_to_hkd(real_price)
        if self.is_cell_invalid(row+2, td_col):
            self.click_set_price_valid()
        price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
        if not price_box:
            self.logger.warning(f"未找到价格框 {date_str}")
            return
        price_box.click()
        price_input_xpath = 'x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[1]/form/div[6]/div/div/div/div[1]/div[1]/div/div/div[1]/input'
        self.page.ele(price_input_xpath, timeout=2)
        modify_btn = self.find_ele_with_retry(self.page, 'x://button[@class="el-button mb-[10px] w-[142px] el-button--primary el-button--medium"]', clear_overlay_func=self.clear_overlays)
        if not modify_btn:
            self.logger.warning(f"未找到修改价格按钮 {date_str}")
            return
        modify_btn.click()
        self.page.ele(price_input_xpath, timeout=2)
        price_input = self.find_ele_with_retry(self.page, price_input_xpath, clear_overlay_func=self.clear_overlays)
        if not price_input:
            self.logger.warning(f"未找到价格输入框 {date_str}")
            return
        price_input.clear()
        price_input.input(str(real_price_hkd))
        save_btn_xpath = 'x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[2]/button[2]'
        save_btn = self.find_ele_with_retry(self.page, save_btn_xpath, clear_overlay_func=self.clear_overlays)
        if not save_btn:
            self.logger.warning(f"未找到保存按钮 {date_str}")
            return
        save_btn.click()
        self.logger.info(f"已保存 {date_str} 价格: {real_price_hkd}")
        # 新增：保存成功后统计点击
        if self.click_statistics:
            self.click_statistics.add_room_click(self.hotel_name, self.tongcheng_hotel_room_name, 1)
        time.sleep(2)
        self.close_popup(date_str)
        if self.should_set_invalid(real_price):
            self.logger.info(f"{date_str} 价格为{real_price}，自动设置为无效")
            price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
            if price_box:
                price_box.click()
                self.click_set_price_invalid()
            else:
                self.logger.warning(f"再次点击价格框失败，无法设置无效 {date_str}")
        time.sleep(0.5)

    def click_set_price_valid(self):
        """
        点击"设置价格有效"按钮
        """
        valid_btn = None
        btns = self.page.eles('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain')
        for btn in btns:
            try:
                if btn.text and "设置价格有效" in btn.text:
                    valid_btn = btn
                    break
            except Exception:
                continue
        if not valid_btn:
            # retry 用 find_ele_with_retry
            valid_btn = self.find_ele_with_retry(
                self.page,
                '.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain',
                clear_overlay_func=self.clear_overlays
            )
            if valid_btn and (not valid_btn.text or "设置价格有效" not in valid_btn.text):
                valid_btn = None
        if valid_btn:
            valid_btn.click()
            # self.logger.info(f"已点击'设置价格有效'按钮，等待页面刷新")
            time.sleep(0.5)

    def should_set_invalid(self, real_price):
        """
        判断价格是否需要设置为无效（如8888、9999）
        """
        return real_price in (8888, 9999)
    def close_popup(self, date_str):
        """
        关闭价格维护弹窗，若多次失败则刷新页面。date_str用于日志输出。
        """
        max_retry = 5
        success = False
        for i in range(max_retry):
            close_btn = self.find_ele_with_retry(self.page, '.el-button el-button--default el-button--small el-button--primary ', max_retry=1, interval=0.5, clear_overlay_func=self.clear_overlays)
            if close_btn:
                try:
                    close_btn.click()
                    # self.logger.debug(f"已点击 class 关闭按钮 {date_str}")
                    time.sleep(0.5)
                    break
                except Exception as e:
                    self.logger.warning(f"点击 class 关闭按钮异常: {e}，重试")
            else:
                self.page.ele('.').input(Keys.ENTER)

    def is_cell_invalid(self, row, col):
        """
        判断指定单元格是否无效，参考多xpath判断方式
        """
        try:
            price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]/span/span/div/p[2]'
            price_text = self.page.ele(price_text_xpath, timeout=0.5).text
            # self.logger.debug(f"第{row}行第{col}格有价格: {price_text}")
            return False
        except Exception:
            try:
                price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]/p'
                price_text = self.page.ele(price_text_xpath, timeout=0.5).text
                self.logger.debug(f"第{row}行第{col}格无效价格: {price_text}")
                return price_text.strip() == '无效'
            except Exception:
                try:
                    price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]'
                    price_text = self.page.ele(price_text_xpath, timeout=0.5).text
                    self.logger.debug(f"第{row}行第{col}格基础获取: {price_text}")
                    return price_text.strip() == '无效'
                except Exception:
                    self.logger.warning(f"第{row}行第{col}格无法获取价格信息")
                    return False

    def currency_cny_to_hkd(self, price_cny, rate=0.91):
        """
        人民币价格转换为港币价格，默认汇率0.86。
        :param price_cny: 人民币价格
        :param rate: 汇率，默认0.86
        :return: 港币价格（四舍五入取整）
        """
        try:
            return int(round(float(price_cny) / rate))
        except Exception:
            return price_cny
    def click_set_price_invalid(self):
        """
        点击"设置价格无效"按钮
        """
        invalid_btn = None
        for _ in range(5):
            btns = self.page.eles('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain')
            for btn in btns:
                try:
                    if btn.text and "设置价格无效" in btn.text:
                        invalid_btn = btn
                        break
                except Exception:
                    continue
            if invalid_btn:
                break
            # retry 用 find_ele_with_retry
            retry_btn = self.find_ele_with_retry(
                self.page,
                '.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain',
                clear_overlay_func=self.clear_overlays
            )
            if retry_btn and retry_btn.text and "设置价格无效" in retry_btn.text:
                invalid_btn = retry_btn
                break
            time.sleep(0.5)
        if invalid_btn:
            invalid_btn.click()
            self.logger.info(f"设置为无效")
            time.sleep(2)
        else:
            self.logger.warning(f"未找到设置价格无效按钮")

    def clear_overlays(self):

        overlay_selectors = [
            '.el-message-box__close.el-icon-close',
            '.el-dialog__headerbtn',
            '.el-overlay',
        ]
        for sel in overlay_selectors:
            try:
                btn = self.page.ele(sel)
                if btn:
                    btn.click()
                    self.logger.debug(f'已关闭遮挡层/弹窗: {sel}')
                    time.sleep(0.5)
            except Exception:
                continue


    def find_ele_with_retry(self, page, selector, max_retry=2, interval=0.5, clear_overlay_func=None):
        """
        查找元素，失败时自动重试，若有清理遮挡层方法则自动调用。
        增加详细日志，便于排查误点。
        """
        for i in range(max_retry):
            ele = page.ele(selector, timeout=2)
            if ele:
                return ele
        self.logger.warning(f"[find_ele_with_retry] 未找到元素: {selector}, url={getattr(page, 'url', None)}")
        return None


    def _generate_price_matrix(self, price_data, rows=4):
        date_list = list(price_data.keys())
        n = len(date_list)
        matrix = [[None for _ in range(n)] for _ in range(rows)]
        for row in range(rows):
            col = row
            while col < n:
                for offset in range(3):
                    c = col + offset
                    if c >= n:
                        break
                    price = price_data[date_list[col]]
                    real_price = 9999 if price is None else int(float(price))
                    matrix[row][c] = real_price
                block_col = col + 3
                if block_col < n:
                    matrix[row][block_col] = 9999
                col += 4
        return matrix, date_list

    def get_actual_price_matrix(self, date_list, room_name,return_page_idx=False):
        """
        采集页面所有价格信息，自动翻页，生成完整实际价格矩阵。
        流程与test/tongcheng_price_maintain.py保持一致，包括采集前后选择房型、翻页、回到第一页、日志输出等。
        :param date_list: 目标日期列表，决定采集天数和矩阵宽度
        :param room_name: 需要采集的同程房型名称，若为None则不切换房型
        :return: matrix[row][col]，内容为数字或9999（无效/无房/None）
        """
        import datetime
        import time
        rows = 4
        n = len(date_list)
        matrix = [[None for _ in range(n)] for _ in range(rows)]
        page_start = 0
        page_idx = 0
        start_index = self.get_today_col_index()
        # 采集前自动选择房型
        self.select_room_by_name(room_name)
        while page_start < n:
            if page_idx == 0:
                格数 = min(15 - start_index + 1, n - page_start)
                td_col_start = start_index
            else:
                格数 = min(14, n - page_start)
                td_col_start = 2
            for row in range(rows):
                for page_col in range(格数):
                    td_col = td_col_start + page_col
                    col = page_start + page_col
                    if col >= n:
                        continue
                    xpath_num = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]/span/span/div/p[2]'
                    xpath_invalid = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]/p'
                    xpath_base = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]'
                    val = None
                    try:
                        val = self.page.ele(xpath_num, timeout=0.5).text
                    except Exception:
                        try:
                            val = self.page.ele(xpath_invalid, timeout=0.5).text
                        except Exception:
                            try:
                                val = self.page.ele(xpath_base, timeout=0.5).text
                            except Exception:
                                val = None
                    if val is None or (isinstance(val, str) and (val.strip() == '无效' or val.strip() == '无房' or val.strip() == '')):
                        matrix[row][col] = 9999
                    else:
                        try:
                            matrix[row][col] = int(val)
                        except Exception:
                            matrix[row][col] = val
            page_start += 格数
            page_idx += 1
            if page_start < n:
                from .page_navigator import TongchengPageNavigator
                navigator = TongchengPageNavigator(self.page, self.logger)
                navigator.click_next_two_weeks()
                # 翻页后重新选择房型
                self.select_room_by_name(room_name)
                time.sleep(2)
        # 回到第一页后重新选择房型，确保后续操作一致
        for i in range(page_idx - 1):
            from .page_navigator import TongchengPageNavigator
            navigator = TongchengPageNavigator(self.page, self.logger)
            navigator.turn_to_prev_page_after_row()
        self.select_room_by_name(room_name)
        if room_name:
            time.sleep(2)  # 等待页面刷新
        return matrix

    def click_prev_two_weeks(self):
        """
        点击'前两周'按钮，不选择房型
        """
        self.logger.debug(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行click_prev_two_weeks")
        prev_btn = self.page.ele('text:前两周')
        if prev_btn:
            prev_btn.click()
            time.sleep(1)
            return True
        else:
            self.logger.warning("未找到'前两周'按钮！")
            return False

    def click_next_two_weeks(self):
        """
        点击'后两周'按钮，并选择房型
        """
        self.logger.debug(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行click_next_two_weeks")
        next_btn = self.page.ele('text:后两周')
        if next_btn:
            next_btn.click()
            time.sleep(2)
            # 每次点击后两周都选择房型
            if self.tongcheng_hotel_room_name:
                self.select_room_by_name(self.tongcheng_hotel_room_name)
            return True
        else:
            self.logger.warning("未找到'后两周'按钮，无法继续维护后续日期！")
            return False

    def select_room_by_name(self, room_name):
        """
        选择指定房型
        """
        # ================= 新增：选不中房型时自动刷新重试 =================
        import time
        max_retry = 3  # 最多重试次数
        for retry in range(1, max_retry + 1):
            room_type_sele = 'x://*[@id="product-app"]/div/div[3]/div[1]/form/div[1]/div/div/div[1]'
            self.page.ele(room_type_sele).click()
            time.sleep(0.5)  # 新增：等待下拉菜单展开
            li_elements = self.page.eles(
                "x://body/div[@class='el-select-dropdown el-popper is-multiple']/div[@class='el-scrollbar']/div[@class='el-select-dropdown__wrap el-scrollbar__wrap']/ul[@class='el-scrollbar__view el-select-dropdown__list']/li")
            found = False
            for i, li_ele in enumerate(li_elements):
                room_name_option = li_ele.ele('x:/div/div').text
                if room_name == room_name_option:
                    try:
                        li_ele.click()
                        self.page.ele("x://div[@class='additionBtns flex items-center justify-center']/div[2]").click()  # 点击确定
                        found = True
                        break
                    except NoRectError:
                        self.logger.warning(f"选项{i+1}: {room_name_option} 无法点击（元素无位置和大小），跳过")
                        continue
            if found:
                break
            else:
                if retry < max_retry:
                    self.logger.warning(f"未找到房型，刷新页面重试，第{retry}次")
                    self.page.refresh()
                    time.sleep(2)  # 等待页面加载
                else:
                    self.logger.error("多次刷新后仍未选中目标房型")
        # ================= 新增功能结束 =================

    def get_today_col_index(self):
        """
        查找表头"今日"所在的th列索引（从1开始）
        """
        for i in range(2, 4):
            try:
                text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                if text == '今日':
                    self.logger.info('开始元素为：', i)
                    self.start_index = i
                    return i
            except Exception:
                continue
        raise Exception('未找到"今日"所在的表头列')

    def get_actual_price_matrix_by_date(self, target_dates, rows=4):
        """
        基于日期匹配获取实际价格矩阵，避免位置错位问题。
        :param target_dates: 目标日期列表
        :param rows: 行数
        :return: 价格矩阵字典 {date: [row1_price, row2_price, ...]}
        """
        price_matrix = {}
        current_page = 0
        processed_dates = set()
        max_pages = (len(target_dates) + 13) // 14  # 每页约14天
        self.logger.info(f"开始获取{len(target_dates)}个日期的价格数据，预计需要{max_pages}页")
        while len(processed_dates) < len(target_dates) and current_page < max_pages:
            # 获取当前页的日期列表
            page_dates = []
            date_to_col = {}  # 日期到列号的映射
            # 获取当前页表头日期
            current_page_dates = self.get_date_sequence_with_context()
            # 构建日期到列的映射
            for i, date_str in enumerate(current_page_dates):
                col_idx = i + 2  # 列索引从2开始
                if date_str in target_dates and date_str not in processed_dates:
                    page_dates.append(date_str)
                    date_to_col[date_str] = col_idx
                    self.logger.info(f"    列{col_idx}: '{date_str}' (匹配)")
                else:
                    self.logger.info(f"    列{col_idx}: '{date_str}' (跳过)")
            self.logger.info(f"第{current_page + 1}页找到{len(page_dates)}个目标日期: {page_dates}")
            # 获取当前页匹配日期的价格
            for date_str in page_dates:
                col_idx = date_to_col[date_str]
                date_prices = []
                for row in range(rows):
                    xpath_num = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]/span/span/div/p[2]'
                    xpath_invalid = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]/p'
                    xpath_base = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]'
                    val = None
                    try:
                        val = self.page.ele(xpath_num, timeout=0.5).text
                    except Exception:
                        try:
                            val = self.page.ele(xpath_invalid, timeout=0.5).text
                        except Exception:
                            try:
                                val = self.page.ele(xpath_base, timeout=0.5).text
                            except Exception:
                                val = None
                    if val is None or (isinstance(val, str) and (val.strip() in ['无效', '无房', ''])):
                        date_prices.append(9999)
                    else:
                        try:
                            date_prices.append(int(val))
                        except Exception:
                            date_prices.append(val)
                price_matrix[date_str] = date_prices
                processed_dates.add(date_str)
            current_page += 1
            # 如果还有未处理的日期且未达到最大页数，翻到下一页
            if len(processed_dates) < len(target_dates) and current_page < max_pages:
                self.logger.info(f"需要更多数据，点击'后两周'进入第{current_page + 1}页")
                self.click_next_two_weeks()
                import time
                time.sleep(2)
            else:
                self.logger.info(f"已处理{len(processed_dates)}/{len(target_dates)}个日期，停止翻页")
                break
        # 回到第一页
        if current_page > 1:
            self.logger.info(f"回到第一页，需要点击{current_page - 1}次'前两周'")
            for i in range(current_page - 1):
                self.click_prev_two_weeks()
                import time
                time.sleep(1)
        self.logger.info(f"最终获取到{len(processed_dates)}个日期的价格数据")
        return price_matrix

    def compare_and_fill_matrix_by_date(self, page_date_list, actual_price_matrix, target_price_dict, threshold=0.05):
        """
        按页面日期与目标价格一一对应比对和维护，支持多行。
        :param page_date_list: 页面表头日期列表，如['2025-07-07', ...]
        :param actual_price_matrix: 页面实际价格矩阵，list of list，行数为房型数，列数为日期数
        :param target_price_dict: 目标价格字典，如{'2025-07-07': [1200, 1300, ...], ...}
        :param threshold: 价格相差阈值
        """
        row_count = len(actual_price_matrix)
        col_count = len(page_date_list)
        for row in range(row_count):
            for col, date_str in enumerate(page_date_list):
                # 获取目标价和实际价
                target_price_list = target_price_dict.get(date_str, None)
                target_price = None
                if target_price_list and row < len(target_price_list):
                    target_price = target_price_list[row]
                actual_price = actual_price_matrix[row][col] if col < len(actual_price_matrix[row]) else None
                # 价格都为None或9999时无需操作
                if (target_price is None or str(target_price) == '9999') and (actual_price is None or str(actual_price) == '9999'):
                    self.logger.info(f"[无需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，均为无效/无房，无需修改")
                    continue
                # 目标为None/9999但实际有价格，需设无效
                if (target_price is None or str(target_price) == '9999') and (actual_price is not None and str(actual_price) != '9999'):
                    self.logger.info(f"[需设无效] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)
                    continue
                # 目标有价格，实际无效/无房，需维护
                if (target_price is not None and str(target_price) != '9999') and (actual_price is None or str(actual_price) == '9999'):
                    self.logger.info(f"[需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，最终填入: {target_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)
                    continue
                # 目标和实际都有价格，判断是否在阈值内
                try:
                    t = int(target_price)
                    a = int(actual_price)
                    if t == 0:
                        close = (a == 0)
                    else:
                        close = abs(t - a) / abs(t) <= threshold
                except Exception:
                    close = False
                if close:
                    self.logger.info(f"[无需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，差值在{int(threshold*100)}%以内，无需修改")
                else:
                    self.logger.info(f"[需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，最终填入: {target_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)

    def fill_price_by_date(self, price_data: dict, room_name, threshold=0.05) -> bool:
        """
        新增：按日期精确维护价格（页面表头日期与目标价格一一对应，只维护有变化的格子）。
        :param price_data: {date_str: price}
        :param room_name: 同程房型名称
        :param threshold: 价格相差阈值
        :return: 是否推送成功
        """
        try:
            self.logger.info("[按日期维护] 尝试按日期精确比对和维护价格...")
            self.tongcheng_hotel_room_name = room_name
            # 1. 获取目标日期列表
            date_list = list(price_data.keys())
            rows = 4
            # 2. 采集页面实际价格（按日期匹配）
            actual_price_matrix_dict = self.get_actual_price_matrix_by_date(date_list, rows)
            # 3. 生成目标价格字典（每个日期对应多行）
            # 这里假设每个日期只维护一行（如单房型），如有多行可扩展
            target_price_dict = {date: [price_data[date] for _ in range(rows)] for date in date_list}
            # 4. 组装页面表头日期顺序
            page_date_list = date_list  # 默认顺序与目标一致
            # 5. 转换实际价格为二维list
            actual_price_matrix = []
            for row in range(rows):
                row_prices = []
                for date in page_date_list:
                    price_list = actual_price_matrix_dict.get(date, [9999]*rows)
                    row_prices.append(price_list[row] if row < len(price_list) else 9999)
                actual_price_matrix.append(row_prices)
            # 6. 比对并维护
            self.compare_and_fill_matrix_by_date(page_date_list, actual_price_matrix, target_price_dict, threshold)
            self.back_to_first_page()
            self.logger.info('[按日期维护] 按日期维护流程完成，已回到首页')
            return True
        except Exception as e:
            self.logger.error(f"[按日期维护] 价格推送失败: {e}")
            self.logger.error(f"[按日期维护] 价格推送失败: {str(e)}")
            return False

    # ========== 依赖辅助方法：get_date_sequence_with_context ==========
    def get_date_sequence_with_context(self):
        """
        获取带上下文的日期序列，用于更准确的日期解析。
        通过分析整个日期序列来推断每个日期的真实意义。
        :return: 日期序列列表
        """
        date_sequence = []
        raw_texts = []
        # 获取所有列的原始文本
        for i in range(2, 16):  # th[2] 到 th[15]
            try:
                date_text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                if date_text:
                    raw_texts.append((i, date_text.strip()))
            except Exception:
                break
        if not raw_texts:
            return []
        self.logger.info(f"原始日期序列: {[text for _, text in raw_texts]}")
        today = datetime.date.today()
        # 解析每个日期
        for i, (col_idx, date_text) in enumerate(raw_texts):
            if date_text == '今日':
                parsed_date = today.strftime('%Y-%m-%d')
            else:
                # 传入列索引用于特殊处理
                parsed_date = self.parse_page_date(date_text, col_idx)
            date_sequence.append(parsed_date)
            self.logger.info(f"  列{col_idx}: '{date_text}' -> '{parsed_date}'")
        return date_sequence

    def parse_page_date(self, date_text, column_index=None):
        """
        解析页面日期文本，将简化格式转换为完整日期。
        :param date_text: 页面显示的日期文本
        :param column_index: 列索引，用于判断是否为第一列
        :return: 完整的日期字符串 YYYY-MM-DD
        """
        if date_text == '今日':
            return datetime.date.today().strftime('%Y-%m-%d')
        try:
            day_num = int(date_text)
            today = datetime.date.today()
            if column_index == 2 and day_num == today.day - 1:
                self.logger.info(f"检测到可能的零点后时间窗口：第一列显示 '{date_text}'，但今天是 {today.day} 号")
                return today.strftime('%Y-%m-%d')
            target_date = self.smart_date_inference(day_num, today, column_index)
            return target_date.strftime('%Y-%m-%d')
        except ValueError:
            return date_text

    def smart_date_inference(self, day_num, today, column_index=None):
        """
        智能日期推断，处理各种边界情况
        :param day_num: 日期数字
        :param today: 今天的日期
        :param column_index: 列索引
        :return: 推断的日期对象
        """
        try:
            target_date = today.replace(day=day_num)
            if target_date < today:
                if column_index == 2 and (today - target_date).days == 1:
                    self.logger.info(f"第一列日期异常：显示 {day_num}，但今天是 {today.day}，可能是零点后的时间窗口")
                    return today
                if today.month == 12:
                    next_month = today.replace(year=today.year + 1, month=1, day=day_num)
                else:
                    next_month = today.replace(month=today.month + 1, day=day_num)
                target_date = next_month
            return target_date
        except ValueError:
            if today.month == 12:
                next_month = today.replace(year=today.year + 1, month=1, day=day_num)
            else:
                next_month = today.replace(month=today.month + 1, day=day_num)
            return next_month

import time

from DrissionPage._functions.keys import Keys


class TongchengDialogOperator:
    def __init__(self, page, logger):
        self.page = page
        self.logger = logger

    def find_ele_with_retry(self, page, selector, max_retry=2, interval=0.5, clear_overlay_func=None):
        """
        查找元素，失败时自动重试，若有清理遮挡层方法则自动调用。
        增加详细日志，便于排查误点。
        """
        for i in range(max_retry):
            ele = page.ele(selector, timeout=2)
            if ele:
                # print(f"[find_ele_with_retry] 第{i+1}次找到元素: {selector}, text={getattr(ele, 'text', None)}, url={getattr(page, 'url', None)}")
                return ele
        # print(f"[find_ele_with_retry] 未找到元素: {selector}, url={getattr(page, 'url', None)}")
        return None
    def close_dialog(self,date_str):
        """关闭弹窗"""
        try:
            max_retry = 5
            success = False
            for i in range(max_retry):
                close_btn = self.find_ele_with_retry(self.page,
                                                     '.el-button el-button--default el-button--small el-button--primary ',
                                                     max_retry=1, interval=0.5)
                if close_btn:
                    try:
                        close_btn.click()
                        print(f"已点击 class 关闭按钮 {date_str}")
                        time.sleep(0.5)
                        break
                    except Exception as e:
                        print(f"点击 class 关闭按钮异常: {e}，重试")
                else:
                    self.page.ele('.').input(Keys.ENTER)
        except Exception as e:
            self.logger.warning(f"关闭弹窗失败: {str(e)}") 
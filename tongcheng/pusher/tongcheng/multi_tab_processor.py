"""
多标签页多进程房型处理器
解决多房型处理时的状态冲突问题，提高处理效率
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from queue import Queue, Empty
import logging
from DrissionPage import ChromiumPage

from .tongcheng_pusher import TongchengPusher
from ...tools.logger import get_pusher_logger

logger = get_pusher_logger()


@dataclass
class RoomTask:
    """房型处理任务"""
    hotel_name: str
    room_name: str
    price_data: dict
    room_index: int
    task_id: str


@dataclass
class ProcessResult:
    """处理结果"""
    task_id: str
    success: bool
    message: str
    room_name: str
    processing_time: float


class TabManager:
    """标签页管理器"""
    
    def __init__(self, max_tabs: int = 3):
        self.max_tabs = max_tabs
        self.available_tabs = Queue()
        self.tab_pushers = {}
        self.tab_lock = threading.Lock()
        
    def initialize_tabs(self, cookies_path: str, headless: bool = False) -> bool:
        """初始化所有标签页"""
        try:
            logger.info(f"初始化 {self.max_tabs} 个标签页...")
            
            for tab_id in range(self.max_tabs):
                # 创建独立的推送器实例
                pusher = TongchengPusher(cookies_path=cookies_path, headless=headless)
                
                if pusher.connect():
                    self.tab_pushers[tab_id] = pusher
                    self.available_tabs.put(tab_id)
                    logger.info(f"标签页 {tab_id} 初始化成功")
                else:
                    logger.error(f"标签页 {tab_id} 初始化失败")
                    return False
                    
            logger.info(f"所有 {self.max_tabs} 个标签页初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化标签页失败: {str(e)}")
            return False
    
    def get_available_tab(self, timeout: float = 30.0) -> Optional[int]:
        """获取可用的标签页"""
        try:
            return self.available_tabs.get(timeout=timeout)
        except Empty:
            logger.warning("获取可用标签页超时")
            return None
    
    def release_tab(self, tab_id: int):
        """释放标签页"""
        with self.tab_lock:
            if tab_id in self.tab_pushers:
                self.available_tabs.put(tab_id)
                logger.debug(f"释放标签页 {tab_id}")
    
    def get_pusher(self, tab_id: int) -> Optional[TongchengPusher]:
        """获取指定标签页的推送器"""
        return self.tab_pushers.get(tab_id)
    
    def cleanup(self):
        """清理所有标签页"""
        logger.info("清理所有标签页...")
        for tab_id, pusher in self.tab_pushers.items():
            try:
                pusher.disconnect()
                logger.debug(f"标签页 {tab_id} 已断开连接")
            except Exception as e:
                logger.warning(f"断开标签页 {tab_id} 连接时出错: {str(e)}")
        
        self.tab_pushers.clear()
        # 清空队列
        while not self.available_tabs.empty():
            try:
                self.available_tabs.get_nowait()
            except Empty:
                break


class MultiTabProcessor:
    """多标签页多进程房型处理器"""
    
    def __init__(self, max_tabs: int = 3, cookies_path: str = "cookies.json", headless: bool = False):
        self.max_tabs = max_tabs
        self.cookies_path = cookies_path
        self.headless = headless
        self.tab_manager = TabManager(max_tabs)
        self.results = []
        
    def initialize(self) -> bool:
        """初始化多标签页处理器"""
        return self.tab_manager.initialize_tabs(self.cookies_path, self.headless)
    
    def process_hotel_rooms(self, hotel_name: str, room_data: List[Dict]) -> Dict[str, Any]:
        """
        使用多标签页并行处理酒店的所有房型
        
        Args:
            hotel_name: 酒店名称
            room_data: 房型数据列表
            
        Returns:
            处理结果统计
        """
        logger.info(f"开始多标签页处理酒店: {hotel_name}, 房型数量: {len(room_data)}")
        
        # 创建房型任务列表
        tasks = []
        for room_index, room in enumerate(room_data):
            room_name = room.get("同程房型名称", "")
            task_id = f"{hotel_name}_{room_name}_{room_index}"
            
            # 构建价格数据（这里需要根据实际数据结构调整）
            price_data = self._build_price_data(room)
            if price_data:
                task = RoomTask(
                    hotel_name=hotel_name,
                    room_name=room_name,
                    price_data=price_data,
                    room_index=room_index,
                    task_id=task_id
                )
                tasks.append(task)
            else:
                logger.warning(f"房型 {room_name} 价格数据为空，跳过")
        
        if not tasks:
            logger.error("没有有效的房型任务")
            return {"success": False, "message": "没有有效的房型任务"}
        
        # 使用线程池并行处理任务
        results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_tabs) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self._process_room_task, task): task 
                for task in tasks
            }
            
            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"任务 {task.task_id} 完成: {result.success}")
                except Exception as e:
                    logger.error(f"任务 {task.task_id} 执行异常: {str(e)}")
                    results.append(ProcessResult(
                        task_id=task.task_id,
                        success=False,
                        message=f"执行异常: {str(e)}",
                        room_name=task.room_name,
                        processing_time=0
                    ))
        
        # 统计结果
        total_time = time.time() - start_time
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        result_summary = {
            "success": success_count > 0,
            "hotel_name": hotel_name,
            "total_rooms": total_count,
            "success_rooms": success_count,
            "failed_rooms": total_count - success_count,
            "total_time": total_time,
            "average_time": total_time / total_count if total_count > 0 else 0,
            "results": results
        }
        
        logger.info(f"酒店 {hotel_name} 处理完成: {success_count}/{total_count} 成功, 耗时: {total_time:.2f}秒")
        return result_summary
    
    def _process_room_task(self, task: RoomTask) -> ProcessResult:
        """处理单个房型任务"""
        start_time = time.time()
        tab_id = None
        
        try:
            # 获取可用标签页
            tab_id = self.tab_manager.get_available_tab()
            if tab_id is None:
                return ProcessResult(
                    task_id=task.task_id,
                    success=False,
                    message="无法获取可用标签页",
                    room_name=task.room_name,
                    processing_time=time.time() - start_time
                )
            
            # 获取推送器
            pusher = self.tab_manager.get_pusher(tab_id)
            if not pusher:
                return ProcessResult(
                    task_id=task.task_id,
                    success=False,
                    message=f"标签页 {tab_id} 推送器不可用",
                    room_name=task.room_name,
                    processing_time=time.time() - start_time
                )
            
            logger.info(f"标签页 {tab_id} 开始处理房型: {task.room_name}")
            
            # 确保进入正确的酒店
            if not pusher.hotel_operator.find_and_enter_hotel(task.hotel_name):
                return ProcessResult(
                    task_id=task.task_id,
                    success=False,
                    message="无法找到或进入酒店",
                    room_name=task.room_name,
                    processing_time=time.time() - start_time
                )
            
            # 处理房型价格
            success = pusher._process_single_room(
                task.hotel_name, 
                task.room_name, 
                task.price_data, 
                task.room_index
            )
            
            processing_time = time.time() - start_time
            
            if success:
                logger.info(f"标签页 {tab_id} 成功处理房型: {task.room_name}, 耗时: {processing_time:.2f}秒")
                return ProcessResult(
                    task_id=task.task_id,
                    success=True,
                    message="处理成功",
                    room_name=task.room_name,
                    processing_time=processing_time
                )
            else:
                return ProcessResult(
                    task_id=task.task_id,
                    success=False,
                    message="房型处理失败",
                    room_name=task.room_name,
                    processing_time=processing_time
                )
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"处理房型任务 {task.task_id} 时发生异常: {str(e)}")
            return ProcessResult(
                task_id=task.task_id,
                success=False,
                message=f"处理异常: {str(e)}",
                room_name=task.room_name,
                processing_time=processing_time
            )
        finally:
            # 释放标签页
            if tab_id is not None:
                self.tab_manager.release_tab(tab_id)
    
    def _build_price_data(self, room_data: Dict) -> Optional[Dict]:
        """构建价格数据"""
        try:
            data = room_data.get('价格数据', [])
            if data and len(data) > 0:
                return data[0]
            return None
        except Exception as e:
            logger.error(f"构建价格数据失败: {str(e)}")
            return None
    
    def cleanup(self):
        """清理资源"""
        self.tab_manager.cleanup()


class MultiTabTongchengPusher(TongchengPusher):
    """支持多标签页的同程推送器"""
    
    def __init__(self, cookies_path: str = "cookies.json", headless: bool = False, max_tabs: int = 3):
        super().__init__(cookies_path, headless)
        self.max_tabs = max_tabs
        self.multi_tab_processor = None
        
    def connect(self) -> bool:
        """连接并初始化多标签页处理器"""
        if super().connect():
            self.multi_tab_processor = MultiTabProcessor(
                max_tabs=self.max_tabs,
                cookies_path=self.cookies_path,
                headless=self.headless
            )
            
            if self.multi_tab_processor.initialize():
                logger.info("多标签页处理器初始化成功")
                return True
            else:
                logger.error("多标签页处理器初始化失败")
                return False
        return False
    
    def push_price(self, data: Dict[str, Any]) -> bool:
        """使用多标签页推送价格"""
        if not self.validate_data(data):
            self.logger.error("数据验证失败")
            return False
            
        hotel_name = data.get("同程酒店名称", "")
        room_data = data.get("房型数据", [])
        
        if not self.multi_tab_processor:
            logger.warning("多标签页处理器未初始化，回退到单标签页模式")
            return super().push_price(data)
        
        # 使用多标签页处理
        result = self.multi_tab_processor.process_hotel_rooms(hotel_name, room_data)
        
        if result["success"]:
            self.log_push_result(
                hotel_name, 
                True, 
                f"多标签页处理成功: {result['success_rooms']}/{result['total_rooms']} 个房型"
            )
            return True
        else:
            self.log_push_result(hotel_name, False, "多标签页处理失败")
            return False
    
    def disconnect(self) -> bool:
        """断开连接并清理多标签页资源"""
        if self.multi_tab_processor:
            self.multi_tab_processor.cleanup()
            self.multi_tab_processor = None
            
        return super().disconnect()

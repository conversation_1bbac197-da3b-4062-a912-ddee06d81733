class TongchengPageNavigator:
    def __init__(self, page, logger):
        self.page = page
        self.logger = logger

    def click_next_two_weeks(self) -> bool:
        """点击'后两周'按钮"""
        try:
            next_btn = self.page.ele('text:后两周')
            if next_btn:
                next_btn.click()
                return True
            else:
                self.logger.warning("未找到'后两周'按钮")
                return False
        except Exception as e:
            self.logger.error(f"点击后两周按钮失败: {str(e)}")
            return False

    def turn_to_prev_page_after_row(self):
        """每行结束后点击'前两周'按钮两次，回到初始日期区间"""
        try:
            prev_btn = self.page.ele('text:前两周')
            if prev_btn:
                prev_btn.click()
        except Exception as e:
            self.logger.warning(f"返回初始日期失败: {str(e)}") 
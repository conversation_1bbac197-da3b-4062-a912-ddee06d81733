from typing import Dict, Any, List
import time
from .tongcheng_pusher import TongchengPusher

class TongchengRule1Pusher(TongchengPusher):
    """
    同程Rule1推送器 - 使用简单价格填充策略
    规则1：按照原始价格直接填充，不使用斜对角填法
    """
    
    def __init__(self, cookies_path: str = "cookies.json", headless: bool = False):
        super().__init__(cookies_path, headless)
        self.logger.info("初始化 TongchengRule1Pusher - 简单价格填充策略")
    
    def _push_price_to_page(self, price_data: Dict[str, float]) -> bool:
        """
        将价格数据推送到同程页面 - Rule1版本
        """
        try:
            # 点击房态维护
            room_status_selector = 'x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[4]/li'
            room_status_btn = self.page.ele(room_status_selector)
            if room_status_btn:
                room_status_btn.click()
                time.sleep(2)
            else:
                self.logger.error("未找到房态维护按钮")
                return False
            
            # 使用Rule1填充策略：只填充第一行，按顺序填充
            self._fill_simple_price_strategy(price_data)
            
            self.logger.info("Rule1价格推送完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Rule1价格推送失败: {self.format_error_message(e)}")
            return False
    
    def _fill_simple_price_strategy(self, price_data: Dict[str, float]):
        """
        Rule1填充策略：简单的顺序填充第一行价格
        """
        self.logger.info("开始执行Rule1简单价格填充策略")
        
        date_list = list(price_data.keys())
        n = len(date_list)
        row = 0  # 只填充第一行
        
        try:
            # 获取第一行房型名称
            room_name_selector = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[1]/div/div[3]/div[@class="room-type"]'
            room_name_element = self.page.ele(room_name_selector)
            room_name = room_name_element.text if room_name_element else "第1行房型"
            self.logger.info(f"Rule1填充房型：{room_name}")
        except Exception:
            self.logger.warning("未能获取第1行房型名称")
        
        # 按日期顺序填充价格
        for i, date_str in enumerate(date_list):
            page_col = (i % 14) + 1  # 页面列位置
            price = price_data[date_str]
            real_price = 9999 if price is None else int(price)
            
            self.logger.info(f"Rule1填充：第1行，第{i+1}格（本页第{page_col}格），日期{date_str}，价格: {real_price}")
            
            # 填充价格
            self._fill_single_cell(row, page_col, i+1, real_price, date_str)
            
            # 检查是否需要翻页（每14格翻页一次）
            if page_col == 14 and i+1 < n:
                self.logger.info("Rule1：已填满14格，点击'后两周'按钮继续...")
                if not self._click_next_two_weeks():
                    self.logger.error("Rule1：无法翻页，停止填充")
                    break
        
        self.logger.info("Rule1简单价格填充策略完成")
    
    def push_price(self, data: Dict[str, Any]) -> bool:
        """
        重写推送方法，添加Rule1特定的日志
        """
        hotel_name = data.get("同程酒店名称", "")
        room_data = data.get("房型数据", [])
        
        self.logger.info(f"Rule1推送器开始处理酒店: {hotel_name}, 房型数量: {len(room_data)}")
        
        # 调用父类的推送方法
        result = super().push_price(data)
        
        if result:
            self.logger.info(f"Rule1推送器成功完成酒店: {hotel_name}")
        else:
            self.logger.error(f"Rule1推送器处理失败酒店: {hotel_name}")
        
        return result



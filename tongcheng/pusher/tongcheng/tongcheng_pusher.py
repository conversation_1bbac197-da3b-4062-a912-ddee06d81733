import json
import time
import os
from typing import Dict, Any, List, Optional
from DrissionPage import ChromiumPage
from tongcheng.pusher.base_pusher import BasePusher
from tongcheng.pusher.tongcheng.hotel_operator import TongchengHotelOperator
from tongcheng.pusher.tongcheng.room_operator import Tong<PERSON>RoomOperator
from tongcheng.pusher.tongcheng.price_operator import TongchengPriceOperator
from tongcheng.pusher.tongcheng.dialog_operator import TongchengDialogOperator
from tongcheng.pusher.tongcheng.page_navigator import TongchengPageNavigator
from tongcheng.pusher.tongcheng.currency_converter import TongchengCurrencyConverter


class TongchengPusher(BasePusher):
    """
    同程推送器实现，集成DrissionPage自动化推送价格到同程平台
    """

    def __init__(self, cookies_path: str = r"cookies.json", headless: bool = False):
        super().__init__()
        self.cookies_path = cookies_path
        self.headless = headless
        self.page: Optional[ChromiumPage] = None
        self.is_connected = False
        self.base_url = "https://ebooking.elong.com"
        self.dashboard_url = "https://ebooking.elong.com/ebkcommon/dashboard#/dashboard"
        # 组合各操作类
        self.hotel_operator = None
        self.room_operator = None
        self.price_operator = None
        self.dialog_operator = None
        self.page_navigator = None
        self.currency_converter = None

    def connect(self) -> bool:
        """
        建立连接，初始化浏览器并登录同程
        """
        try:
            self.logger.info("开始连接同程平台...")
            print("DEBUG: cookies_path =", self.cookies_path)
            print("DEBUG: file exists =", os.path.exists(self.cookies_path))
            if not os.path.exists(self.cookies_path):
                self.logger.error(f"Cookies文件不存在: {self.cookies_path}")
                return False
            self.page = ChromiumPage()
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            self.page.get(self.base_url)
            self.page.set.cookies(cookies)
            self.page.get(self.dashboard_url)
            time.sleep(3)
            # 初始化操作类
            self.hotel_operator = TongchengHotelOperator(self.page, self.logger)
            self.room_operator = TongchengRoomOperator(self.page, self.logger)
            self.currency_converter = TongchengCurrencyConverter()
            self.price_operator = TongchengPriceOperator(self.page, self.logger, self.currency_converter)
            self.dialog_operator = TongchengDialogOperator(self.page, self.logger)
            self.page_navigator = TongchengPageNavigator(self.page, self.logger)
            if self._verify_login():
                self.is_connected = True
                self.logger.info("成功连接并登录同程平台")
                return True
            else:
                self.logger.error("登录验证失败")
                return False
        except Exception as e:
            self.logger.error(f"连接同程平台失败: {self.format_error_message(e)}")
            return False

    def disconnect(self) -> bool:
        try:
            if self.page:
                self.page.quit()
                self.page = None
            self.is_connected = False
            self.logger.info("已断开与同程平台的连接")
            return True
        except Exception as e:
            self.logger.error(f"断开连接失败: {self.format_error_message(e)}")
            return False

    def push_price(self, data: Dict[str, Any], by_date: bool = False) -> bool:
        if not self.validate_data(data):
            self.logger.error("数据验证失败")
            return False
        hotel_name = data.get("同程酒店名称", "")
        room_data = data.get("房型数据", [])

        self.log_push_attempt(hotel_name, len(room_data))
        # 新增：find_and_enter_hotel重试机制，最多重试3次
        max_retry = 3
        for attempt in range(1, max_retry + 1):
            if self.hotel_operator.find_and_enter_hotel(hotel_name):
                break
            else:
                self.logger.warning(f"第{attempt}次尝试进入酒店失败: {hotel_name}")
                time.sleep(1)
        else:
            # 多次重试后仍失败，直接返回
            self.log_push_result(hotel_name, False, "多次重试后仍无法进入酒店，推送失败")
            return False

        time.sleep(3)
        self.logger.info(f"开始推送价格到同程酒店: {hotel_name}")

        try:
            failed_rooms = []  # 新增：用于收集所有推送失败的同程房型名称
            for room in room_data:
                # 新增：房价维护按钮重试机制，最多重试3次
                for btn_attempt in range(1, max_retry + 1):
                    price_maintain_btn = self.page.ele(
                        'x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[4]/li')
                    if price_maintain_btn:
                        try:
                            price_maintain_btn.click()
                            time.sleep(2)
                            break
                        except Exception as e:
                            self.logger.warning(f"第{btn_attempt}次点击房价维护按钮异常: {e}")
                            time.sleep(1)
                    else:
                        self.logger.warning(f"第{btn_attempt}次未找到房价维护按钮")
                        time.sleep(1)
                else:
                    # 多次重试后仍失败，直接返回
                    self.log_push_result(hotel_name, False, "多次重试后仍未找到房价维护按钮，推送失败")
                    return False
                room_name = room.get("同程房型名称", "")
                # 新增：以同程房型名称为准进行查找，若为空或找不到直接报错
                if not room_name:
                    failed_rooms.append(room_name)
                    self.log_push_result(hotel_name, False, "同程房型名称为空，推送失败", failed_rooms=failed_rooms)
                    return False
                for room_attempt in range(1, max_retry + 1):
                    # 只用同程房型名称查找
                    found_room = False
                    if hasattr(self.room_operator, 'find_and_select_room'):
                        found_room = self.room_operator.find_and_select_room(room_name)
                    else:
                        # 如果没有该方法，默认True（兼容原有逻辑）
                        found_room = True
                    if found_room:
                        break
                    else:
                        self.logger.warning(f"未找到同程房型：{room_name}，刷新页面重试，第{room_attempt}次")
                        self.page.refresh()
                        time.sleep(1)
                else:
                    # 多次刷新后仍未选中目标同程房型，直接返回
                    failed_rooms.append(room_name)
                    self.log_push_result(hotel_name, False, f"未找到同程房型：{room_name}，推送失败", failed_rooms=failed_rooms)
                    return False
                print(f'正在维护{hotel_name}-{room_name}价格')
                # 构建价格数据
                price_data = self._build_price_data(room)
                if not price_data:
                    failed_rooms.append(room_name)
                    self.log_push_result(hotel_name, False, "价格数据为空", failed_rooms=failed_rooms)
                    return False
                # 推送每个价格
                if by_date:
                    success = self.price_operator.fill_price_by_date(price_data, room_name)
                else:
                    success = self.price_operator.fill_price(price_data, room_name)
                if not success:
                    failed_rooms.append(room_name)
            # 新增：如果有失败房型，记录详细日志
            if failed_rooms:
                self.log_push_result(hotel_name, False, "部分房型推送失败", failed_rooms=failed_rooms)
                return False
        except Exception as e:
            error_msg = self.format_error_message(e)
            self.log_push_result(hotel_name, False, error_msg)
            return False

    def _verify_login(self) -> bool:
        try:
            time.sleep(2)
            return True
        except Exception:
            return False

    def _build_price_data(self, room_data):
        data = room_data['价格数据']
        return data[0]


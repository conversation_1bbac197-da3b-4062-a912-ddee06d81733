from abc import ABC, abstractmethod
from typing import Dict, Any, List
from ..tools.logger import get_pusher_logger

# 使用统一日志管理器
logger = get_pusher_logger()

class BasePusher(ABC):
    """
    基础推送器抽象类，定义推送接口规范
    """
    
    def __init__(self):
        self.logger = logger
        
    @abstractmethod
    def push_price(self, data: Dict[str, Any]) -> bool:
        """
        推送价格数据的抽象方法
        :param data: 推送数据，包含酒店名称和房型数据
        :return: 推送是否成功
        """
        pass
        
    @abstractmethod
    def connect(self) -> bool:
        """
        建立连接的抽象方法
        :return: 连接是否成功
        """
        pass
        
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开连接的抽象方法
        :return: 断开是否成功
        """
        pass
        
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """
        验证推送数据格式
        :param data: 待验证的数据
        :return: 数据是否有效
        """
        required_fields = ["同程酒店名称", "房型数据"]
        for field in required_fields:
            if field not in data:
                self.logger.error(f"推送数据缺少必要字段: {field}")
                return False
                
        if not isinstance(data["房型数据"], list):
            self.logger.error("房型数据必须是列表格式")
            return False
            
        return True
        
    def log_push_attempt(self, hotel_name: str, room_count: int):
        """
        记录推送尝试
        """
        self.logger.info(f"尝试推送酒店: {hotel_name}, 房型数量: {room_count}")
        
    def log_push_result(self, hotel_name: str, success: bool, message: str = ""):
        """
        记录推送结果
        """
        if success:
            self.logger.info(f"推送成功: {hotel_name} - {message}")
        else:
            self.logger.error(f"推送失败: {hotel_name} - {message}")
            
    def format_error_message(self, error: Exception) -> str:
        """
        格式化错误信息
        """
        return f"推送异常: {type(error).__name__}: {str(error)}"

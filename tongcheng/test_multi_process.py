#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程酒店价格同步系统 - 多进程版本测试脚本

用于测试多进程多标签页版本的基本功能
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    SINGLE_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import MultiTabConfig


def test_config_creation():
    """测试配置创建"""
    print("🧪 测试配置创建...")
    
    try:
        # 测试预定义配置
        configs = [
            ("单进程配置", SINGLE_PROCESS_CONFIG),
            ("安全模式配置", SAFE_MULTI_PROCESS_CONFIG),
            ("默认配置", MultiProcessConfig.create_default()),
            ("快速模式配置", MultiProcessConfig.create_fast_mode())
        ]
        
        for name, config in configs:
            assert config is not None, f"{name} 创建失败"
            assert config.max_processes > 0, f"{name} 进程数无效"
            assert config.multi_tab_config is not None, f"{name} 多标签页配置缺失"
            assert config.multi_tab_config.max_tabs > 0, f"{name} 标签页数无效"
            print(f"  ✅ {name}: {config.max_processes}进程 × {config.multi_tab_config.max_tabs}标签页")
        
        # 测试自定义配置
        custom_tab_config = MultiTabConfig(max_tabs=2, headless=True)
        custom_config = MultiProcessConfig(
            max_processes=2,
            multi_tab_config=custom_tab_config
        )
        assert custom_config.max_processes == 2
        assert custom_config.multi_tab_config.max_tabs == 2
        assert custom_config.multi_tab_config.headless == True
        print(f"  ✅ 自定义配置: 2进程 × 2标签页 (无头模式)")
        
        print("✅ 配置创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置创建测试失败: {str(e)}")
        return False


def test_orchestrator_initialization():
    """测试协调器初始化"""
    print("🧪 测试协调器初始化...")
    
    try:
        # 测试默认初始化
        orchestrator1 = MultiProcessOrchestrator()
        assert orchestrator1.config is not None
        assert orchestrator1.process_manager is not None
        assert orchestrator1.workflow is not None
        print("  ✅ 默认初始化成功")
        
        # 测试自定义配置初始化
        config = SINGLE_PROCESS_CONFIG
        orchestrator2 = MultiProcessOrchestrator(config)
        assert orchestrator2.config == config
        assert orchestrator2.config.max_processes == 1
        print("  ✅ 自定义配置初始化成功")
        
        print("✅ 协调器初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 协调器初始化测试失败: {str(e)}")
        return False


def test_health_check():
    """测试健康检查"""
    print("🧪 测试健康检查...")
    
    try:
        orchestrator = MultiProcessOrchestrator(SINGLE_PROCESS_CONFIG)
        
        # 执行健康检查
        health = orchestrator.health_check()
        
        assert isinstance(health, dict), "健康检查结果应该是字典"
        assert "healthy" in health, "健康检查结果应包含healthy字段"
        assert "components" in health, "健康检查结果应包含components字段"
        assert "timestamp" in health, "健康检查结果应包含timestamp字段"
        
        print(f"  健康状态: {'✅ 健康' if health['healthy'] else '❌ 异常'}")
        print(f"  组件状态: {health.get('components', {})}")
        print(f"  配置有效: {health.get('config_valid', False)}")
        
        print("✅ 健康检查测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 健康检查测试失败: {str(e)}")
        return False


def test_system_status():
    """测试系统状态获取"""
    print("🧪 测试系统状态获取...")
    
    try:
        orchestrator = MultiProcessOrchestrator(SAFE_MULTI_PROCESS_CONFIG)
        
        # 获取系统状态
        status = orchestrator.get_system_status()
        
        assert isinstance(status, dict), "系统状态应该是字典"
        assert "multi_process_config" in status, "系统状态应包含多进程配置"
        assert "error_count" in status, "系统状态应包含错误数量"
        
        config_info = status["multi_process_config"]
        print(f"  进程数: {config_info.get('max_processes', 'Unknown')}")
        print(f"  多进程启用: {config_info.get('enable_multi_process', 'Unknown')}")
        print(f"  每进程标签页数: {config_info.get('max_tabs_per_process', 'Unknown')}")
        print(f"  错误数量: {status.get('error_count', 0)}")
        
        print("✅ 系统状态获取测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 系统状态获取测试失败: {str(e)}")
        return False


def test_system_reset():
    """测试系统重置"""
    print("🧪 测试系统重置...")
    
    try:
        orchestrator = MultiProcessOrchestrator(SINGLE_PROCESS_CONFIG)
        
        # 记录一些错误（模拟）
        orchestrator.error_handler.record_error("测试错误", {"test": "data"})
        
        # 检查错误数量
        initial_errors = len(orchestrator.error_handler.get_errors())
        assert initial_errors > 0, "应该有测试错误"
        
        # 执行系统重置
        orchestrator.reset_system()
        
        # 检查重置后的状态
        after_errors = len(orchestrator.error_handler.get_errors())
        assert after_errors == 0, "重置后应该没有错误"
        
        print(f"  重置前错误数: {initial_errors}")
        print(f"  重置后错误数: {after_errors}")
        
        print("✅ 系统重置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 系统重置测试失败: {str(e)}")
        return False


def test_performance_comparison():
    """测试性能对比计算"""
    print("🧪 测试性能对比计算...")
    
    try:
        configs = [
            ("单进程", SINGLE_PROCESS_CONFIG),
            ("安全模式", SAFE_MULTI_PROCESS_CONFIG),
            ("默认模式", MultiProcessConfig.create_default()),
            ("快速模式", MultiProcessConfig.create_fast_mode())
        ]
        
        print("  理论并行度对比:")
        for name, config in configs:
            parallelism = config.max_processes * config.multi_tab_config.max_tabs
            print(f"    {name}: {config.max_processes}进程 × {config.multi_tab_config.max_tabs}标签页 = {parallelism}x并行度")
        
        print("✅ 性能对比计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能对比计算测试失败: {str(e)}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行多进程版本测试套件")
    print("=" * 60)
    
    tests = [
        ("配置创建", test_config_creation),
        ("协调器初始化", test_orchestrator_initialization),
        ("健康检查", test_health_check),
        ("系统状态获取", test_system_status),
        ("系统重置", test_system_reset),
        ("性能对比计算", test_performance_comparison)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {len(tests)}")
    print(f"通过: {passed} ✅")
    print(f"失败: {failed} ❌")
    print(f"成功率: {passed/len(tests)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！多进程版本基本功能正常")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关功能")
        return False


def main():
    """主函数"""
    print("🧪 同程酒店价格同步系统 - 多进程版本测试")
    print("用于验证多进程多标签页版本的基本功能")
    print()
    
    while True:
        print("请选择测试选项:")
        print("1. 运行所有测试")
        print("2. 配置创建测试")
        print("3. 协调器初始化测试")
        print("4. 健康检查测试")
        print("5. 系统状态测试")
        print("6. 系统重置测试")
        print("7. 性能对比测试")
        print("8. 退出")
        
        choice = input("\n请输入选择 (1-8): ").strip()
        
        if choice == "1":
            run_all_tests()
        elif choice == "2":
            test_config_creation()
        elif choice == "3":
            test_orchestrator_initialization()
        elif choice == "4":
            test_health_check()
        elif choice == "5":
            test_system_status()
        elif choice == "6":
            test_system_reset()
        elif choice == "7":
            test_performance_comparison()
        elif choice == "8":
            print("👋 测试结束!")
            break
        else:
            print("❌ 无效选择，请重试")
        
        print("\n" + "-" * 60 + "\n")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {str(e)}")

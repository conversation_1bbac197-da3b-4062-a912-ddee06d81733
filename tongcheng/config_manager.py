import json
import os

def get_badazhou_config():
    """
    读取八大洲渠道的通用配置参数。
    """
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'badazhou_config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_feishu_config():
    """
    读取飞书相关配置参数。
    """
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'feishu_config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

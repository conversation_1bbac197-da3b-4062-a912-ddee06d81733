#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程酒店价格同步系统 - 多进程多标签页版本

这是一个高性能的自动化酒店价格同步系统，主要特性包括：
1. 多进程并行处理不同酒店，大幅提升处理效率
2. 每个进程内使用多标签页处理房型，解决状态冲突问题
3. 智能错误恢复和重试机制
4. 详细的性能监控和资源管理
5. 灵活的配置模式选择

使用方法：
python main_multi_process.py
"""

import sys
import os
import time
from datetime import datetime, timedelta
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    DEFAULT_MULTI_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG,
    SINGLE_PROCESS_CONFIG
)
from tongcheng.tools.logger import get_workflow_logger

logger = get_workflow_logger()

# ================= 日志记录功能 =================
LOG_FILE = 'price_change_multi_process.log'
COUNT_FILE = 'price_change_count_multi_process.txt'


def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🚀 同程酒店价格同步系统 - 多进程多标签页版本")
    print("=" * 80)
    print("✨ 新特性:")
    print("   • 多进程并行处理酒店，大幅提升效率")
    print("   • 每进程多标签页处理房型，解决状态冲突")
    print("   • 智能错误恢复和重试机制")
    print("   • 详细的性能监控和资源管理")
    print("   • 灵活的配置模式选择")
    print("=" * 80)
    print()


def select_processing_mode() -> MultiProcessConfig:
    """选择处理模式"""
    print("📋 请选择处理模式:")
    print("1. 单进程模式 (兼容模式，适合调试)")
    print("2. 安全模式 (2进程×2标签页，稳定可靠)")
    print("3. 默认模式 (2进程×3标签页，平衡性能)")
    print("4. 快速模式 (3进程×5标签页，最高效率)")
    print("5. 自定义模式")
    print("6. 查看模式对比")

    while True:
        choice = input("\n请输入选择 (1-6): ").strip()

        if choice == "1":
            print("✅ 选择了单进程模式")
            return SINGLE_PROCESS_CONFIG
        elif choice == "2":
            print("✅ 选择了安全模式 (2进程×2标签页)")
            return SAFE_MULTI_PROCESS_CONFIG
        elif choice == "3":
            print("✅ 选择了默认模式 (2进程×3标签页)")
            return DEFAULT_MULTI_PROCESS_CONFIG
        elif choice == "4":
            print("✅ 选择了快速模式 (3进程×5标签页)")
            return FAST_MULTI_PROCESS_CONFIG
        elif choice == "5":
            return create_custom_config()
        elif choice == "6":
            show_mode_comparison()
            continue
        else:
            print("❌ 无效选择，请重试")


def show_mode_comparison():
    """显示模式对比"""
    print("\n" + "=" * 70)
    print("📊 处理模式性能对比分析")
    print("=" * 70)

    # 假设处理6个酒店，每个酒店4个房型，每个房型30秒
    hotel_count = 6
    room_per_hotel = 4
    time_per_room = 30

    modes = {
        "单进程模式": {
            "processes": 1, "tabs": 1,
            "config": SINGLE_PROCESS_CONFIG
        },
        "安全模式": {
            "processes": 2, "tabs": 2,
            "config": SAFE_MULTI_PROCESS_CONFIG
        },
        "默认模式": {
            "processes": 2, "tabs": 3,
            "config": DEFAULT_MULTI_PROCESS_CONFIG
        },
        "快速模式": {
            "processes": 3, "tabs": 5,
            "config": FAST_MULTI_PROCESS_CONFIG
        }
    }

    print(f"假设场景: {hotel_count}个酒店, 每酒店{room_per_hotel}个房型, 每房型{time_per_room}秒")
    print("-" * 70)
    print(f"{'模式':<12} {'进程数':<6} {'标签页':<6} {'并行度':<6} {'预计时间':<10} {'加速比':<8}")
    print("-" * 70)

    baseline_time = hotel_count * room_per_hotel * time_per_room  # 单线程基准时间

    for mode_name, info in modes.items():
        processes = info["processes"]
        tabs = info["tabs"]
        parallelism = processes * tabs

        # 计算预计时间（简化计算）
        if processes == 1 and tabs == 1:
            estimated_time = baseline_time
        else:
            # 进程级并行 + 标签页级并行
            hotels_per_process = hotel_count / processes
            rooms_per_tab = room_per_hotel / tabs
            estimated_time = hotels_per_process * rooms_per_tab * time_per_room

        speedup = baseline_time / estimated_time if estimated_time > 0 else 1.0

        print(f"{mode_name:<12} {processes:<6} {tabs:<6} {parallelism:<6} {estimated_time/60:.1f}分钟{'':<3} {speedup:.1f}x")

    print("-" * 70)
    print("💡 提示:")
    print("   • 进程数过多可能导致资源竞争")
    print("   • 标签页数过多可能影响浏览器稳定性")
    print("   • 建议根据机器性能选择合适模式")
    print("=" * 70)
    print()


def create_custom_config() -> MultiProcessConfig:
    """创建自定义配置"""
    print("\n🔧 自定义配置模式")

    try:
        max_processes = int(input("请输入进程数量 (1-5, 推荐2-3): ").strip())
        if max_processes < 1 or max_processes > 5:
            print("⚠️ 进程数量超出推荐范围，使用默认值2")
            max_processes = 2

        max_tabs = int(input("请输入每进程标签页数量 (1-8, 推荐2-5): ").strip())
        if max_tabs < 1 or max_tabs > 8:
            print("⚠️ 标签页数量超出推荐范围，使用默认值3")
            max_tabs = 3

        headless_input = input("是否使用无头模式? (y/N): ").strip().lower()
        headless = headless_input == 'y'

        # 创建自定义多标签页配置
        from tongcheng.config.multi_tab_config import MultiTabConfig
        multi_tab_config = MultiTabConfig(
            max_tabs=max_tabs,
            headless=headless,
            enable_multi_tab=max_tabs > 1
        )

        # 创建自定义多进程配置
        custom_config = MultiProcessConfig(
            max_processes=max_processes,
            enable_multi_process=max_processes > 1,
            multi_tab_config=multi_tab_config
        )

        print(f"✅ 自定义配置创建成功:")
        print(f"   进程数量: {max_processes}")
        print(f"   标签页数量: {max_tabs}")
        print(f"   无头模式: {'是' if headless else '否'}")
        print(f"   理论并行度: {max_processes * max_tabs}x")

        return custom_config

    except ValueError:
        print("❌ 输入无效，使用默认配置")
        return DEFAULT_MULTI_PROCESS_CONFIG


def load_counts():
    """加载历史统计数据"""
    total = 0
    room_type_count = defaultdict(int)
    room_date_count = defaultdict(int)

    if os.path.exists(COUNT_FILE):
        try:
            with open(COUNT_FILE, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('总更改次数:'):
                        total = int(line.split(':')[1].strip())
                    elif ':' in line and ' ' not in line:
                        # 房型总次数
                        parts = line.split(':')
                        if len(parts) == 2:
                            room_type_count[parts[0]] = int(parts[1])
                    elif ' ' in line and ':' in line:
                        # 房型+日期
                        parts = line.split(':')
                        if len(parts) == 2:
                            key = parts[0]
                            room_date_count[key] = int(parts[1])
        except Exception as e:
            logger.warning(f"加载历史统计数据失败: {str(e)}")

    return total, room_type_count, room_date_count


def save_counts(total, room_type_count, room_date_count):
    """保存统计数据"""
    try:
        with open(COUNT_FILE, 'w', encoding='utf-8') as f:
            f.write(f'总更改次数: {total}\n\n')
            f.write('各房型总更改次数:\n')
            for room, cnt in room_type_count.items():
                f.write(f'{room}: {cnt}\n')
            f.write('\n各房型各日期更改次数:\n')
            for key, cnt in room_date_count.items():
                f.write(f'{key}: {cnt}\n')
    except Exception as e:
        logger.error(f"保存统计数据失败: {str(e)}")


def log_execution_results(result: dict):
    """记录执行结果"""
    try:
        now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total, room_type_count, room_date_count = load_counts()

        # 从结果中提取变更信息
        summary = result.get("summary", {})
        detailed_results = result.get("detailed_results", [])

        change_count = 0

        with open(LOG_FILE, 'a', encoding='utf-8') as logf:
            # 记录总体执行信息
            logf.write(f'\n[{now_str}] ===== 多进程执行结果 =====\n')
            logf.write(f'处理酒店数: {summary.get("total_hotels", 0)}\n')
            logf.write(f'成功酒店数: {summary.get("success_hotels", 0)}\n')
            logf.write(f'失败酒店数: {summary.get("failed_hotels", 0)}\n')
            logf.write(f'成功率: {summary.get("success_rate", 0):.1f}%\n')
            logf.write(f'总处理时间: {summary.get("total_processing_time", 0):.2f}秒\n')
            logf.write(f'处理房型总数: {summary.get("total_rooms_processed", 0)}\n')

            # 记录详细的酒店处理结果
            for hotel_result in detailed_results:
                hotel_name = hotel_result.hotel_name
                success = hotel_result.success
                room_count = hotel_result.room_count
                processing_time = hotel_result.processing_time

                status = "成功" if success else "失败"
                logf.write(f'酒店: {hotel_name}, 状态: {status}, 房型数: {room_count}, 耗时: {processing_time:.2f}秒\n')

                if success:
                    # 假设每个房型都有变更（实际应该从详细结果中获取）
                    change_count += room_count
                    total += room_count
                    room_type_count[hotel_name] += room_count

        save_counts(total, room_type_count, room_date_count)

        print(f"📝 本次处理结果已记录到日志文件")
        print(f"   本次变更: {change_count} 条")
        print(f"   累计变更: {total} 条")

    except Exception as e:
        logger.error(f"记录执行结果失败: {str(e)}")


def run_single_execution(config: MultiProcessConfig):
    """执行单次处理"""
    print(f"\n🚀 开始执行多进程处理...")
    print(f"   配置: {config.max_processes}进程 × {config.multi_tab_config.max_tabs}标签页")
    print(f"   理论并行度: {config.max_processes * config.multi_tab_config.max_tabs}x")

    start_time = time.time()

    try:
        # 创建多进程协调器
        orchestrator = MultiProcessOrchestrator(config)

        # 执行主工作流程
        result = orchestrator.main_workflow()

        execution_time = time.time() - start_time

        # 显示执行结果
        print(f"\n📊 执行完成，总耗时: {execution_time:.2f}秒")

        if result["status"] == "success":
            summary = result["summary"]
            print(f"✅ 处理成功!")
            print(f"   处理酒店: {summary['success_hotels']}/{summary['total_hotels']} 个")
            print(f"   成功率: {summary['success_rate']:.1f}%")
            print(f"   处理房型: {summary['total_rooms_processed']} 个")
            print(f"   平均每酒店: {summary['average_processing_time']:.2f}秒")

            # 记录执行结果
            log_execution_results(result)

        else:
            print(f"⚠️ 处理完成但有问题: {result['message']}")

        return result

    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ 执行异常，耗时: {execution_time:.2f}秒")
        print(f"   错误信息: {str(e)}")
        logger.error(f"单次执行异常: {str(e)}")
        return None


def run_continuous_mode(config: MultiProcessConfig):
    """连续执行模式"""
    print(f"\n🔄 进入连续执行模式")
    print(f"   每2小时自动执行一次")
    print(f"   按 Ctrl+C 可以停止")

    execution_count = 0

    try:
        while True:
            execution_count += 1
            print(f"\n{'='*60}")
            print(f"🔄 第 {execution_count} 次执行 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*60}")

            # 执行单次处理
            result = run_single_execution(config)

            if result:
                print(f"✅ 第 {execution_count} 次执行完成")
            else:
                print(f"❌ 第 {execution_count} 次执行失败")

            print(f"\n⏰ 等待2小时后进行下次执行...")
            print(f"   下次执行时间: {(datetime.now() + timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S')}")

            # 等待2小时
            time.sleep(7200)

    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，连续执行模式已停止")
        print(f"   总共执行了 {execution_count} 次")
    except Exception as e:
        print(f"\n❌ 连续执行模式异常: {str(e)}")
        logger.error(f"连续执行模式异常: {str(e)}")


def main():
    """主函数"""
    print_banner()

    # 选择处理模式
    config = select_processing_mode()

    # 选择执行方式
    print(f"\n📋 请选择执行方式:")
    print("1. 单次执行")
    print("2. 连续执行 (每2小时自动执行)")
    print("3. 系统健康检查")
    print("4. 退出")

    while True:
        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            run_single_execution(config)
            break
        elif choice == "2":
            run_continuous_mode(config)
            break
        elif choice == "3":
            # 系统健康检查
            print(f"\n🔍 执行系统健康检查...")
            orchestrator = MultiProcessOrchestrator(config)
            health = orchestrator.health_check()

            if health["healthy"]:
                print("✅ 系统健康状态良好")
            else:
                print("❌ 系统健康检查发现问题")
                if "error" in health:
                    print(f"   错误: {health['error']}")

            print(f"   组件状态: {health.get('components', {})}")
            print(f"   配置有效: {health.get('config_valid', False)}")
            continue
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        logger.error(f"主程序异常: {str(e)}")

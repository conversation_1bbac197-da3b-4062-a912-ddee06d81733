from typing import List, Dict, Any
from ..tools.logger import get_workflow_logger, LoggerManager
from ..models.hotel_models import Hotel, RoomType
from ..models.mapping_models import RoomMapping
from ..models.price_models import PriceCalendar
from ..config_manager import get_feishu_config
from ..matcher.feishu.feishu_hotel_name_getter import HotelNameGetter, safe_strip, parse_rich_text
from ..matcher.feishu.feishu_room_name_getter import RoomNameGetter
from ..fetcher.badazhou_fetcher import BadazhouFetcher
from .data_processor import DataProcessor
from .pusher_manager import PusherManager
from datetime import datetime, timedelta
import json

# 使用统一日志管理器
logger = get_workflow_logger()

class ClickStatistics:
    """
    点击统计类，用于跟踪各种点击次数
    """
    def __init__(self):
        self.total_clicks = 0  # 总点击次数
        self.hotel_clicks = {}  # 每个酒店的点击次数 {hotel_name: count}
        self.room_clicks = {}  # 每个房型的点击次数 {hotel_name: {room_name: count}}
        self.hotel_room_counts = {}  # 每个酒店的房型数量 {hotel_name: room_count}
        self.start_time = datetime.now()

    def add_room_click(self, hotel_name: str, room_name: str, click_count: int = 1):
        """添加房型点击次数"""
        # 更新总点击次数
        self.total_clicks += click_count

        # 更新酒店点击次数
        if hotel_name not in self.hotel_clicks:
            self.hotel_clicks[hotel_name] = 0
        self.hotel_clicks[hotel_name] += click_count

        # 更新房型点击次数
        if hotel_name not in self.room_clicks:
            self.room_clicks[hotel_name] = {}
        if room_name not in self.room_clicks[hotel_name]:
            self.room_clicks[hotel_name][room_name] = 0
        self.room_clicks[hotel_name][room_name] += click_count

    def add_hotel_room_count(self, hotel_name: str, room_count: int):
        """记录酒店的房型数量"""
        self.hotel_room_counts[hotel_name] = room_count

    def get_statistics_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        return {
            "执行时间": {
                "开始时间": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "结束时间": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "总耗时": str(duration)
            },
            "总点击次数": self.total_clicks,
            "处理酒店数量": len(self.hotel_clicks),
            "酒店点击统计": self.hotel_clicks,
            "房型点击统计": self.room_clicks,
            "酒店房型数量": self.hotel_room_counts
        }

    def print_statistics(self):
        """输出详细统计信息到日志"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        logger.info("\n" + "=" * 60)
        logger.info("点击统计报告")
        logger.info("=" * 60)

        # 基本统计信息
        logger.info(f"执行时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总耗时: {duration}")
        logger.info(f"总点击次数: {self.total_clicks}")
        logger.info(f"处理酒店数量: {len(self.hotel_clicks)}")

        # 计算平均值
        total_rooms = sum(self.hotel_room_counts.values())
        if len(self.hotel_clicks) > 0:
            avg_clicks_per_hotel = self.total_clicks / len(self.hotel_clicks)
            logger.info(f"平均每个酒店点击次数: {avg_clicks_per_hotel:.2f}")

        if total_rooms > 0:
            avg_clicks_per_room = self.total_clicks / total_rooms
            logger.info(f"平均每个房型点击次数: {avg_clicks_per_room:.2f}")

        logger.info("\n" + "-" * 60)
        logger.info("各酒店点击统计详情")
        logger.info("-" * 60)

        # 按点击次数排序显示酒店
        sorted_hotels = sorted(self.hotel_clicks.items(), key=lambda x: x[1], reverse=True)

        for i, (hotel_name, clicks) in enumerate(sorted_hotels, 1):
            room_count = self.hotel_room_counts.get(hotel_name, 0)
            logger.info(f"{i}. 酒店: {hotel_name}")
            logger.info(f"   总点击次数: {clicks}")
            logger.info(f"   房型数量: {room_count}")

            if room_count > 0:
                avg_clicks_per_room_hotel = clicks / room_count
                logger.info(f"   平均每房型点击: {avg_clicks_per_room_hotel:.2f}")

            if hotel_name in self.room_clicks:
                # 按点击次数排序显示房型
                sorted_rooms = sorted(self.room_clicks[hotel_name].items(), key=lambda x: x[1], reverse=True)
                logger.info(f"   房型点击详情:")
                for room_name, room_clicks in sorted_rooms:
                    logger.info(f"     - {room_name}: {room_clicks} 次")
            logger.info("")  # 空行分隔

        # 最高和最低点击统计
        if self.hotel_clicks:
            max_hotel = max(self.hotel_clicks.items(), key=lambda x: x[1])
            min_hotel = min(self.hotel_clicks.items(), key=lambda x: x[1])

            logger.info("-" * 60)
            logger.info("极值统计")
            logger.info("-" * 60)
            logger.info(f"点击次数最多的酒店: {max_hotel[0]} ({max_hotel[1]} 次)")
            logger.info(f"点击次数最少的酒店: {min_hotel[0]} ({min_hotel[1]} 次)")

            # 找出点击次数最多和最少的房型
            all_room_clicks = []
            for hotel_rooms in self.room_clicks.values():
                for room_name, clicks in hotel_rooms.items():
                    all_room_clicks.append((room_name, clicks))

            if all_room_clicks:
                max_room = max(all_room_clicks, key=lambda x: x[1])
                min_room = min(all_room_clicks, key=lambda x: x[1])
                logger.info(f"点击次数最多的房型: {max_room[0]} ({max_room[1]} 次)")
                logger.info(f"点击次数最少的房型: {min_room[0]} ({min_room[1]} 次)")

        logger.info("\n" + "=" * 60)
        logger.info("统计报告结束")
        logger.info("=" * 60)

    def save_to_file(self, filename: str = None):
        """保存统计信息到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"click_statistics_{timestamp}.json"

        statistics = self.get_statistics_summary()

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(statistics, f, ensure_ascii=False, indent=2)
            logger.info(f"统计信息已保存到文件: {filename}")
        except Exception as e:
            logger.error(f"保存统计信息失败: {str(e)}")

class WorkflowCoordinator:
    """
    流程协调模块，负责协调任务流程。
    """
    def __init__(self, error_handler=None, performance_monitor=None):
        self.error_handler = error_handler
        self.performance_monitor = performance_monitor

        # 初始化配置
        self.config = get_feishu_config()

        # 初始化统计对象
        self.click_statistics = ClickStatistics()

        # 初始化组件
        self._init_components()

    def _init_components(self):
        """初始化各个组件"""
        hotel_config = self.config["hotel_table"]
        room_config = self.config["room_table"]

        # 初始化飞书数据获取器
        self.hotel_getter = HotelNameGetter(
            app_token=hotel_config["app_token"],
            table_id=hotel_config["table_id"],
            view_id=hotel_config["view_id"],
            use_tenant_token=hotel_config["use_tenant_token"]
        )

        self.room_getter = RoomNameGetter(
            app_token=room_config["app_token"],
            table_id=room_config["table_id"],
            view_id=room_config["view_id"],
            use_tenant_token=room_config["use_tenant_token"]
        )

        # 初始化其他组件
        self.fetcher = BadazhouFetcher()
        self.data_processor = DataProcessor(self.error_handler, self.performance_monitor)
        # 初始化推送管理器，使用test目录下的cookies.json
        self.pusher_manager = PusherManager(
            error_handler=self.error_handler,
            performance_monitor=self.performance_monitor,
            cookies_path=r"D:\mycode\crawler\project\tongcheng_hotel\tongcheng\cookies.json"
        )

    def execute_main_workflow(self) -> Dict[str, Any]:
        """执行主工作流程"""
        logger.info("=== 开始执行主工作流程 ===")

        try:
            # 1. 获取飞书酒店数据
            hotels_data = self._get_hotels_data()

            # 2. 获取飞书房型映射关系
            room_mappings = self._get_room_mappings()

            # 3. 获取日期范围
            start_date, days = self.data_processor.get_date_range(self.config)
            logger.info(f"查询日期范围: {start_date} 起 {days} 天")

            # 新增：生成date_list
            date_list = [(datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days)]

            # 4. 处理每个酒店
            self._process_hotels(hotels_data, room_mappings, start_date, days, date_list)

            # 5. 输出统计信息
            self.click_statistics.print_statistics()
            self.click_statistics.save_to_file()

            logger.info("=== 主工作流程执行完成 ===")
            return {
                "status": "success",
                "message": "工作流程执行成功",
                "statistics": self.click_statistics.get_statistics_summary()
            }

        except Exception as e:
            logger.error(f"主工作流程执行异常: {str(e)}")
            if self.error_handler:
                self.error_handler.record_error("工作流程异常", {"错误原因": str(e)})
            raise e
        finally:
            # 确保断开推送器连接
            try:
                if hasattr(self, 'pusher_manager'):
                    self.pusher_manager.disconnect()
            except Exception as e:
                logger.warning(f"断开推送器连接时出现问题: {str(e)}")

    def _get_hotels_data(self) -> List[Dict]:
        """获取飞书酒店数据"""
        logger.info("步骤1: 获取飞书酒店数据")
        if self.error_handler:
            hotels_data = self.error_handler.retry_with_config(
                self.hotel_getter.get_hotel_names,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"]
            )
        else:
            hotels_data = self.hotel_getter.get_hotel_names()

        logger.info(f"获取到 {len(hotels_data)} 个酒店")
        return hotels_data

    def _get_room_mappings(self) -> List[Dict]:
        """获取飞书房型映射关系"""
        logger.info("步骤2: 获取飞书房型映射关系")
        if self.error_handler:
            room_mappings = self.error_handler.retry_with_config(
                self.room_getter.get_room_names,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"]
            )
        else:
            room_mappings = self.room_getter.get_room_names()

        logger.info(f"获取到 {len(room_mappings)} 个房型映射关系")
        return room_mappings

    def _process_hotels(self, hotels_data: List[Dict], room_mappings: List[Dict],
                       start_date: str, days: int, date_list: List[str]):
        """处理所有酒店"""
        for hotel in hotels_data:
            if self.performance_monitor:
                self.performance_monitor.increment_counter("processed_hotels")
            if not self.data_processor.validate_hotel_data(hotel):
                continue
            hotel_code = parse_rich_text(hotel.get("八大洲酒店编码", ""))
            hotel_name = parse_rich_text(hotel.get("八大洲酒店名称", ""))
            tongcheng_hotel_name = parse_rich_text(hotel.get("同程酒店名称", ""))
            hotel_code = str(hotel_code).strip() if hotel_code is not None else ""
            hotel_name = str(hotel_name).strip() if hotel_name is not None else ""
            tongcheng_hotel_name = str(tongcheng_hotel_name).strip() if tongcheng_hotel_name is not None else ""
            logger.info(f"处理酒店: {hotel_name} ({hotel_code})")
            try:
                hotel_price_data = self._get_hotel_price_data(hotel_code, hotel_name, start_date, days)
                if not hotel_price_data:
                    continue
                processed_hotels = self.data_processor.process_badazhou_data(hotel_price_data, date_list)
                if not processed_hotels:
                    if self.error_handler:
                        self.error_handler.record_error("数据处理失败", {
                            "酒店名称": hotel_name,
                            "酒店编码": hotel_code,
                            "错误原因": "数据处理器返回空结果"
                        })
                    continue
                # 直接用processed_hotels作为房型列表
                all_rooms = processed_hotels
                if not all_rooms:
                    if self.error_handler:
                        self.error_handler.record_error("房型数据为空", {
                            "酒店名称": hotel_name,
                            "酒店编码": hotel_code,
                            "错误原因": "未获取到房型数据"
                        })
                    continue
                hotel_room_mappings = self.data_processor.filter_hotel_room_mappings(
                    room_mappings, hotel_name
                )
                logger.info(f"开始房型匹配，酒店: {hotel_name}")
                matched_rooms = self.data_processor.match_rooms(all_rooms, hotel_room_mappings)

                # 记录酒店房型数量
                self.click_statistics.add_hotel_room_count(tongcheng_hotel_name, len(matched_rooms))

                if matched_rooms:
                    logger.info(f"推送价格数据，酒店: {tongcheng_hotel_name}")
                    # 推送价格数据并获取点击统计
                    push_result = self.pusher_manager.push_price_data_with_statistics(
                        tongcheng_hotel_name, matched_rooms, self.click_statistics
                    )
                else:
                    logger.warning(f"酒店 {hotel_name} 没有匹配到任何房型")
            except Exception as e:
                if self.error_handler:
                    self.error_handler.record_error("酒店处理异常", {
                        "酒店名称": hotel_name,
                        "酒店编码": hotel_code,
                        "错误原因": str(e)
                    })
                continue

    def _get_hotel_price_data(self, hotel_code: str, hotel_name: str,
                             start_date: str, days: int) -> List[Dict]:
        """获取酒店价格数据"""
        logger.info(f"获取酒店 {hotel_name} 的价格数据")

        if self.error_handler:
            price_result = self.error_handler.retry_with_config(
                self.fetcher.fetch_price_range_sliding_window,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"],
                [hotel_code], start_date, days
            )
        else:
            price_result = self.fetcher.fetch_price_range_sliding_window([hotel_code], start_date, days)

        hotel_price_data = price_result.get(hotel_code, [])
        if not hotel_price_data:
            if self.error_handler:
                self.error_handler.record_error("价格数据获取失败", {
                    "酒店名称": hotel_name,
                    "酒店编码": hotel_code,
                    "错误原因": "接口返回空数据"
                })
            return []

        return hotel_price_data

    def coordinate(self, tasks):
        """
        协调任务流程，tasks 为任务列表。
        """
        logger.info(f"开始协调 {len(tasks)} 个任务")
        for task in tasks:
            try:
                if callable(task):
                    task()
                else:
                    logger.warning(f"任务不可执行: {task}")
            except Exception as e:
                logger.error(f"任务执行失败: {str(e)}")
                if self.error_handler:
                    self.error_handler.record_error("任务执行失败", {"错误原因": str(e)})
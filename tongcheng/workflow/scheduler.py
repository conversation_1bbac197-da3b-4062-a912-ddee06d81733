import logging
from typing import List, Callable, Any, Dict
from datetime import datetime

logger = logging.getLogger(__name__)

class Scheduler:
    """
    任务调度模块，负责调度单个或批量任务。
    """
    def __init__(self):
        self.tasks = []
        self.running = False
        
    def schedule_task(self, task: Callable):
        """
        调度单个任务，task 可以是任意可执行对象。
        """
        if callable(task):
            self.tasks.append({
                "task": task,
                "scheduled_time": datetime.now(),
                "status": "pending"
            })
            logger.info(f"任务已调度: {task.__name__ if hasattr(task, '__name__') else 'unknown'}")
        else:
            logger.error(f"任务不可执行: {task}")
            
    def schedule_tasks(self, tasks: List[Callable]):
        """
        批量调度任务
        """
        for task in tasks:
            self.schedule_task(task)
            
    def execute_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """
        执行所有已调度的任务
        """
        results = []
        self.running = True
        
        logger.info(f"开始执行 {len(self.tasks)} 个已调度的任务")
        
        for task_info in self.tasks:
            task = task_info["task"]
            task_name = task.__name__ if hasattr(task, '__name__') else 'unknown'
            
            try:
                logger.info(f"执行任务: {task_name}")
                task_info["status"] = "running"
                task_info["start_time"] = datetime.now()
                
                result = task()
                
                task_info["status"] = "completed"
                task_info["end_time"] = datetime.now()
                task_info["result"] = result
                
                logger.info(f"任务执行成功: {task_name}")
                results.append(task_info)
                
            except Exception as e:
                task_info["status"] = "failed"
                task_info["end_time"] = datetime.now()
                task_info["error"] = str(e)
                
                logger.error(f"任务执行失败: {task_name}, 错误: {str(e)}")
                results.append(task_info)
                
        self.running = False
        logger.info("所有任务执行完成")
        return results
        
    def clear_tasks(self):
        """
        清空所有任务
        """
        self.tasks.clear()
        logger.info("已清空所有任务")
        
    def get_task_status(self) -> List[Dict[str, Any]]:
        """
        获取所有任务状态
        """
        return self.tasks.copy()
        
    def is_running(self) -> bool:
        """
        检查调度器是否正在运行
        """
        return self.running 
"""
多标签页多进程性能监控器
专门监控多标签页多进程环境下的性能指标
"""

import time
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict
import statistics

from ..tools.logger import get_workflow_logger

logger = get_workflow_logger()


@dataclass
class ProcessMetrics:
    """进程性能指标"""
    process_id: int
    start_time: float
    end_time: Optional[float] = None
    hotels_processed: int = 0
    rooms_processed: int = 0
    success_count: int = 0
    error_count: int = 0
    total_processing_time: float = 0.0
    avg_hotel_time: float = 0.0
    tab_count: int = 0
    
    @property
    def duration(self) -> float:
        """进程运行时长"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.hotels_processed
        return (self.success_count / total * 100) if total > 0 else 0.0
    
    @property
    def throughput(self) -> float:
        """吞吐量（酒店/秒）"""
        duration = self.duration
        return self.hotels_processed / duration if duration > 0 else 0.0


@dataclass
class TabMetrics:
    """标签页性能指标"""
    tab_id: int
    process_id: int
    rooms_processed: int = 0
    total_processing_time: float = 0.0
    avg_room_time: float = 0.0
    success_count: int = 0
    error_count: int = 0
    utilization_time: float = 0.0  # 实际使用时间
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.rooms_processed
        return (self.success_count / total * 100) if total > 0 else 0.0


@dataclass
class SystemMetrics:
    """系统整体性能指标"""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    total_processes: int = 0
    total_tabs: int = 0
    total_hotels: int = 0
    total_rooms: int = 0
    successful_hotels: int = 0
    failed_hotels: int = 0
    
    @property
    def duration(self) -> float:
        """总运行时长"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def success_rate(self) -> float:
        """整体成功率"""
        return (self.successful_hotels / self.total_hotels * 100) if self.total_hotels > 0 else 0.0
    
    @property
    def theoretical_parallel(self) -> int:
        """理论并行度"""
        return self.total_processes * self.total_tabs
    
    @property
    def actual_throughput(self) -> float:
        """实际吞吐量（酒店/秒）"""
        duration = self.duration
        return self.total_hotels / duration if duration > 0 else 0.0


class MultiTabPerformanceMonitor:
    """多标签页多进程性能监控器"""
    
    def __init__(self):
        self.system_metrics = SystemMetrics()
        self.process_metrics: Dict[int, ProcessMetrics] = {}
        self.tab_metrics: Dict[str, TabMetrics] = {}  # key: f"{process_id}_{tab_id}"
        
        self.monitoring_active = False
        self.monitor_thread = None
        self.lock = threading.Lock()
        
        # 性能数据收集
        self.performance_samples = []
        self.error_events = []
        
    def start_monitoring(self, total_processes: int, tabs_per_process: int):
        """开始性能监控"""
        with self.lock:
            self.system_metrics = SystemMetrics()
            self.system_metrics.total_processes = total_processes
            self.system_metrics.total_tabs = tabs_per_process
            self.monitoring_active = True
            
            logger.info(f"开始多标签页多进程性能监控: {total_processes}进程 × {tabs_per_process}标签页")
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止性能监控"""
        with self.lock:
            self.monitoring_active = False
            self.system_metrics.end_time = time.time()
            
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
            
        logger.info("多标签页多进程性能监控已停止")
    
    def record_process_start(self, process_id: int, tab_count: int):
        """记录进程开始"""
        with self.lock:
            self.process_metrics[process_id] = ProcessMetrics(
                process_id=process_id,
                start_time=time.time(),
                tab_count=tab_count
            )
            logger.debug(f"记录进程 {process_id} 开始，标签页数: {tab_count}")
    
    def record_process_end(self, process_id: int):
        """记录进程结束"""
        with self.lock:
            if process_id in self.process_metrics:
                self.process_metrics[process_id].end_time = time.time()
                logger.debug(f"记录进程 {process_id} 结束")
    
    def record_hotel_processed(self, process_id: int, success: bool, processing_time: float, room_count: int):
        """记录酒店处理结果"""
        with self.lock:
            if process_id in self.process_metrics:
                metrics = self.process_metrics[process_id]
                metrics.hotels_processed += 1
                metrics.rooms_processed += room_count
                metrics.total_processing_time += processing_time
                
                if success:
                    metrics.success_count += 1
                    self.system_metrics.successful_hotels += 1
                else:
                    metrics.error_count += 1
                    self.system_metrics.failed_hotels += 1
                
                # 更新平均时间
                metrics.avg_hotel_time = metrics.total_processing_time / metrics.hotels_processed
                
                # 更新系统指标
                self.system_metrics.total_hotels += 1
                self.system_metrics.total_rooms += room_count
                
                logger.debug(f"记录进程 {process_id} 处理酒店: 成功={success}, 耗时={processing_time:.2f}s, 房型数={room_count}")
    
    def record_tab_activity(self, process_id: int, tab_id: int, room_success: bool, processing_time: float):
        """记录标签页活动"""
        with self.lock:
            key = f"{process_id}_{tab_id}"
            
            if key not in self.tab_metrics:
                self.tab_metrics[key] = TabMetrics(
                    tab_id=tab_id,
                    process_id=process_id
                )
            
            metrics = self.tab_metrics[key]
            metrics.rooms_processed += 1
            metrics.total_processing_time += processing_time
            metrics.utilization_time += processing_time
            
            if room_success:
                metrics.success_count += 1
            else:
                metrics.error_count += 1
            
            # 更新平均时间
            metrics.avg_room_time = metrics.total_processing_time / metrics.rooms_processed
            
            logger.debug(f"记录标签页 {process_id}_{tab_id} 活动: 成功={room_success}, 耗时={processing_time:.2f}s")
    
    def record_error(self, process_id: int, error_type: str, error_message: str):
        """记录错误事件"""
        with self.lock:
            error_event = {
                'timestamp': datetime.now(),
                'process_id': process_id,
                'error_type': error_type,
                'error_message': error_message
            }
            self.error_events.append(error_event)
            
            # 只保留最近100个错误
            if len(self.error_events) > 100:
                self.error_events = self.error_events[-100:]
            
            logger.debug(f"记录错误事件: 进程={process_id}, 类型={error_type}")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 每5秒收集一次性能样本
                self._collect_performance_sample()
                time.sleep(5.0)
            except Exception as e:
                logger.error(f"性能监控循环异常: {str(e)}")
                time.sleep(10.0)
    
    def _collect_performance_sample(self):
        """收集性能样本"""
        with self.lock:
            sample = {
                'timestamp': datetime.now(),
                'active_processes': len([m for m in self.process_metrics.values() if m.end_time is None]),
                'total_hotels_processed': self.system_metrics.total_hotels,
                'total_rooms_processed': self.system_metrics.total_rooms,
                'current_success_rate': self.system_metrics.success_rate,
                'current_throughput': self.system_metrics.actual_throughput
            }
            
            self.performance_samples.append(sample)
            
            # 只保留最近100个样本
            if len(self.performance_samples) > 100:
                self.performance_samples = self.performance_samples[-100:]
    
    def get_real_time_stats(self) -> Dict[str, Any]:
        """获取实时统计信息"""
        with self.lock:
            return {
                'system_metrics': {
                    'duration': self.system_metrics.duration,
                    'total_processes': self.system_metrics.total_processes,
                    'total_tabs': self.system_metrics.total_tabs,
                    'theoretical_parallel': self.system_metrics.theoretical_parallel,
                    'total_hotels': self.system_metrics.total_hotels,
                    'total_rooms': self.system_metrics.total_rooms,
                    'success_rate': self.system_metrics.success_rate,
                    'actual_throughput': self.system_metrics.actual_throughput
                },
                'active_processes': len([m for m in self.process_metrics.values() if m.end_time is None]),
                'recent_errors': len([e for e in self.error_events if datetime.now() - e['timestamp'] < timedelta(minutes=5)])
            }
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成详细性能报告"""
        with self.lock:
            # 基本统计
            report = {
                'summary': {
                    'total_duration': self.system_metrics.duration,
                    'total_processes': self.system_metrics.total_processes,
                    'total_tabs': self.system_metrics.total_tabs,
                    'theoretical_parallel': self.system_metrics.theoretical_parallel,
                    'total_hotels': self.system_metrics.total_hotels,
                    'total_rooms': self.system_metrics.total_rooms,
                    'successful_hotels': self.system_metrics.successful_hotels,
                    'failed_hotels': self.system_metrics.failed_hotels,
                    'overall_success_rate': self.system_metrics.success_rate,
                    'actual_throughput': self.system_metrics.actual_throughput
                }
            }
            
            # 进程性能分析
            if self.process_metrics:
                process_stats = []
                throughputs = []
                success_rates = []
                
                for process_id, metrics in self.process_metrics.items():
                    stats = {
                        'process_id': process_id,
                        'duration': metrics.duration,
                        'hotels_processed': metrics.hotels_processed,
                        'rooms_processed': metrics.rooms_processed,
                        'success_rate': metrics.success_rate,
                        'throughput': metrics.throughput,
                        'avg_hotel_time': metrics.avg_hotel_time,
                        'tab_count': metrics.tab_count
                    }
                    process_stats.append(stats)
                    
                    if metrics.throughput > 0:
                        throughputs.append(metrics.throughput)
                    if metrics.hotels_processed > 0:
                        success_rates.append(metrics.success_rate)
                
                report['process_analysis'] = {
                    'individual_stats': process_stats,
                    'avg_throughput': statistics.mean(throughputs) if throughputs else 0,
                    'min_throughput': min(throughputs) if throughputs else 0,
                    'max_throughput': max(throughputs) if throughputs else 0,
                    'avg_success_rate': statistics.mean(success_rates) if success_rates else 0,
                    'throughput_std': statistics.stdev(throughputs) if len(throughputs) > 1 else 0
                }
            
            # 标签页性能分析
            if self.tab_metrics:
                tab_stats = []
                utilizations = []
                
                for key, metrics in self.tab_metrics.items():
                    stats = {
                        'process_id': metrics.process_id,
                        'tab_id': metrics.tab_id,
                        'rooms_processed': metrics.rooms_processed,
                        'success_rate': metrics.success_rate,
                        'avg_room_time': metrics.avg_room_time,
                        'utilization_time': metrics.utilization_time
                    }
                    tab_stats.append(stats)
                    
                    if metrics.utilization_time > 0:
                        utilizations.append(metrics.utilization_time)
                
                report['tab_analysis'] = {
                    'individual_stats': tab_stats,
                    'avg_utilization': statistics.mean(utilizations) if utilizations else 0,
                    'total_tabs_used': len(tab_stats)
                }
            
            # 错误分析
            if self.error_events:
                error_types = defaultdict(int)
                recent_errors = []
                
                for error in self.error_events:
                    error_types[error['error_type']] += 1
                    if datetime.now() - error['timestamp'] < timedelta(hours=1):
                        recent_errors.append({
                            'timestamp': error['timestamp'].isoformat(),
                            'process_id': error['process_id'],
                            'error_type': error['error_type'],
                            'error_message': error['error_message']
                        })
                
                report['error_analysis'] = {
                    'total_errors': len(self.error_events),
                    'error_types': dict(error_types),
                    'recent_errors': recent_errors[-10:]  # 最近10个错误
                }
            
            # 性能趋势
            if self.performance_samples:
                report['performance_trends'] = {
                    'sample_count': len(self.performance_samples),
                    'latest_sample': self.performance_samples[-1] if self.performance_samples else None,
                    'throughput_trend': [s['current_throughput'] for s in self.performance_samples[-10:]]
                }
            
            # 效率分析
            theoretical_max = self.system_metrics.theoretical_parallel
            actual_throughput = self.system_metrics.actual_throughput
            
            if theoretical_max > 0 and self.system_metrics.duration > 0:
                # 理论上每个并行单元每秒处理1个酒店的效率
                theoretical_throughput = theoretical_max  # 理论最大吞吐量
                efficiency = (actual_throughput / theoretical_throughput * 100) if theoretical_throughput > 0 else 0
                
                report['efficiency_analysis'] = {
                    'theoretical_max_parallel': theoretical_max,
                    'theoretical_max_throughput': theoretical_throughput,
                    'actual_throughput': actual_throughput,
                    'parallel_efficiency': efficiency,
                    'speedup_factor': actual_throughput  # 相对于单线程的加速比
                }
            
            return report
    
    def get_current_status(self) -> str:
        """获取当前状态描述"""
        with self.lock:
            active_processes = len([m for m in self.process_metrics.values() if m.end_time is None])
            
            if not self.monitoring_active:
                return "监控已停止"
            elif active_processes == 0:
                return "等待进程启动"
            else:
                return f"监控中 - {active_processes}个活跃进程"
    
    def reset(self):
        """重置监控器"""
        with self.lock:
            self.system_metrics = SystemMetrics()
            self.process_metrics.clear()
            self.tab_metrics.clear()
            self.performance_samples.clear()
            self.error_events.clear()
            self.monitoring_active = False
            
        logger.info("性能监控器已重置")

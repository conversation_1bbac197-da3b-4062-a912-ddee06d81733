"""
多进程协调器 - 管理多进程多标签页的酒店价格处理流程
"""

import time
from typing import Dict, Any, List
from datetime import datetime, timedelta

from ..tools.logger import get_workflow_logger
from ..config.multi_process_config import MultiProcessConfig
from .process_manager import ProcessManager, HotelTask, ProcessResult
from .workflow import WorkflowCoordinator
from .error_handler import ErrorHandler
from .performance_monitor import PerformanceMonitor
from .state_manager import StateManager
from .resource_manager import ResourceManager

logger = get_workflow_logger()


class MultiProcessOrchestrator:
    """
    多进程协调器 - 负责多进程多标签页的整体调度与流程控制
    """

    def __init__(self, config: MultiProcessConfig = None):
        self.config = config or MultiProcessConfig.create_default()

        # 初始化管理模块
        self.error_handler = ErrorHandler()
        self.performance_monitor = PerformanceMonitor()
        self.state_manager = StateManager()
        self.resource_manager = ResourceManager()

        # 初始化进程管理器
        self.process_manager = ProcessManager(self.config)

        # 初始化单进程工作流协调器（用于数据获取）
        self.workflow = WorkflowCoordinator(
            error_handler=self.error_handler,
            performance_monitor=self.performance_monitor
        )

        # 计算总并行度
        total_parallel = self.config.max_processes * self.config.multi_tab_config.max_tabs
        logger.info(f"多进程协调器初始化完成 - 进程数: {self.config.max_processes}, 每进程标签页数: {self.config.multi_tab_config.max_tabs}, 总并行度: {total_parallel}x")

    def main_workflow(self) -> Dict[str, Any]:
        """主工作流程 - 多进程多标签页版本"""
        logger.info("=== 多进程协调器开始执行主工作流程 ===")

        try:
            # 开始性能监控
            self.performance_monitor.start_monitoring()

            # 1. 获取基础数据（单进程执行）
            hotels_data, room_mappings, date_info = self._get_base_data()

            # 2. 准备酒店任务
            hotel_tasks = self._prepare_hotel_tasks(hotels_data, room_mappings, date_info)

            if not hotel_tasks:
                logger.warning("没有找到需要处理的酒店任务")
                return {
                    "status": "warning",
                    "message": "没有找到需要处理的酒店任务",
                    "summary": {"total_hotels": 0, "success_hotels": 0, "failed_hotels": 0}
                }

            # 3. 执行多进程处理
            results = self._execute_multi_process_workflow(hotel_tasks)

            # 4. 生成执行报告
            summary = self.process_manager.get_processing_summary(results)
            report = self._generate_final_report(summary, results)

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            logger.info("=== 多进程协调器主工作流程执行完成 ===")

            return {
                "status": "success",
                "message": "多进程工作流程执行成功",
                "summary": summary,
                "execution_report": report,
                "detailed_results": results
            }

        except Exception as e:
            logger.error(f"多进程协调器主工作流程执行异常: {str(e)}")
            self.error_handler.record_error("多进程工作流程异常", {"错误原因": str(e)})

            # 即使出错也要停止监控和生成报告
            self.performance_monitor.stop_monitoring()

            raise e

    def _get_base_data(self) -> tuple:
        """获取基础数据（酒店信息、房型映射、日期信息）"""
        logger.info("步骤1: 获取基础数据")

        # 获取飞书酒店数据
        hotels_data = self.workflow._get_hotels_data()

        # 获取飞书房型映射关系
        room_mappings = self.workflow._get_room_mappings()

        # 获取日期范围
        start_date, days = self.workflow.data_processor.get_date_range(self.workflow.config)
        date_list = [(datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days)]

        date_info = {
            "start_date": start_date,
            "days": days,
            "date_list": date_list
        }

        logger.info(f"获取到 {len(hotels_data)} 个酒店, {len(room_mappings)} 个房型映射, 日期范围: {start_date} 起 {days} 天")

        return hotels_data, room_mappings, date_info

    def _prepare_hotel_tasks(self, hotels_data: List[Dict], room_mappings: List[Dict], date_info: Dict) -> List[HotelTask]:
        """准备酒店处理任务"""
        logger.info("步骤2: 准备酒店处理任务")

        hotel_tasks = []

        for hotel_index, hotel in enumerate(hotels_data):
            try:
                hotel_code = hotel.get("酒店编码", "")
                hotel_name = hotel.get("同程酒店名称", "")
                badazhou_hotel_name = hotel.get("八大洲酒店名称", "")

                if not hotel_code or not hotel_name:
                    logger.warning(f"酒店数据不完整，跳过: {hotel}")
                    continue

                # 获取酒店价格数据
                hotel_price_data = self._get_hotel_price_data(hotel_code, badazhou_hotel_name, date_info)

                if not hotel_price_data:
                    logger.warning(f"酒店 {hotel_name} 没有价格数据，跳过")
                    continue

                # 处理房型匹配
                matched_rooms = self.workflow.data_processor.match_rooms(hotel_price_data, room_mappings)

                if not matched_rooms:
                    logger.warning(f"酒店 {hotel_name} 没有匹配的房型，跳过")
                    continue

                # 创建酒店任务
                task = HotelTask(
                    hotel_id=hotel_code,
                    hotel_name=hotel_name,
                    hotel_data={
                        "同程酒店名称": hotel_name,
                        "八大洲酒店名称": badazhou_hotel_name,
                        "房型数据": matched_rooms
                    },
                    room_mappings=room_mappings,
                    task_id=f"hotel_{hotel_code}_{hotel_index}",
                    priority=0
                )

                hotel_tasks.append(task)
                logger.info(f"准备酒店任务: {hotel_name}, 房型数量: {len(matched_rooms)}")

            except Exception as e:
                logger.error(f"准备酒店任务失败: {hotel.get('同程酒店名称', 'Unknown')}, 错误: {str(e)}")
                self.error_handler.record_error("准备酒店任务失败", {
                    "酒店": hotel.get("同程酒店名称", "Unknown"),
                    "错误原因": str(e)
                })
                continue

        logger.info(f"准备完成，共 {len(hotel_tasks)} 个酒店任务")
        return hotel_tasks

    def _get_hotel_price_data(self, hotel_code: str, badazhou_hotel_name: str, date_info: Dict) -> List[Dict]:
        """获取单个酒店的价格数据"""
        try:
            # 使用八大洲获取器获取价格数据
            hotel_price_data = self.workflow.badazhou_fetcher.get_hotel_price(
                hotel_code,
                date_info["start_date"],
                date_info["days"]
            )

            if hotel_price_data:
                # 处理八大洲数据
                processed_data = self.workflow.data_processor.process_badazhou_data(
                    hotel_price_data,
                    date_info["date_list"]
                )
                return processed_data
            else:
                logger.warning(f"八大洲接口未返回酒店 {badazhou_hotel_name}({hotel_code}) 的价格数据")
                return []

        except Exception as e:
            logger.error(f"获取酒店 {badazhou_hotel_name}({hotel_code}) 价格数据失败: {str(e)}")
            self.error_handler.record_error("获取酒店价格数据失败", {
                "酒店编码": hotel_code,
                "八大洲酒店名称": badazhou_hotel_name,
                "错误原因": str(e)
            })
            return []

    def _execute_multi_process_workflow(self, hotel_tasks: List[HotelTask]) -> List[ProcessResult]:
        """执行多进程工作流程"""
        logger.info(f"步骤3: 执行多进程处理 - {len(hotel_tasks)} 个酒店任务")

        # 按优先级排序任务
        hotel_tasks.sort(key=lambda x: x.priority, reverse=True)

        # 执行多进程处理
        results = self.process_manager.process_hotels_parallel(hotel_tasks)

        # 记录处理结果
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count

        logger.info(f"多进程处理完成: 成功 {success_count} 个, 失败 {failed_count} 个")

        return results

    def _generate_final_report(self, summary: Dict[str, Any], results: List[ProcessResult]) -> Dict[str, Any]:
        """生成最终执行报告"""
        errors = self.error_handler.get_errors()
        performance_report = self.performance_monitor.generate_report(errors)

        # 构建详细报告
        report = {
            "execution_summary": summary,
            "performance_metrics": performance_report,
            "error_summary": {
                "total_errors": len(errors),
                "recent_errors": errors[-5:] if errors else []
            },
            "hotel_results": [
                {
                    "hotel_name": r.hotel_name,
                    "success": r.success,
                    "processing_time": r.processing_time,
                    "room_count": r.room_count,
                    "message": r.message
                }
                for r in results
            ],
            "system_info": {
                "多进程模式": "启用" if self.config.enable_multi_process else "禁用",
                "进程数量": self.config.max_processes,
                "每进程标签页数": self.config.multi_tab_config.max_tabs,
                "理论并行度": self.config.max_processes * self.config.multi_tab_config.max_tabs
            }
        }

        return report

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "multi_process_config": {
                "max_processes": self.config.max_processes,
                "enable_multi_process": self.config.enable_multi_process,
                "max_tabs_per_process": self.config.multi_tab_config.max_tabs
            },
            "performance_stats": self.performance_monitor.get_stats(),
            "error_count": len(self.error_handler.get_errors()),
            "recent_errors": self.error_handler.get_errors()[-3:] if self.error_handler.get_errors() else []
        }

    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            # 检查基本组件
            components_status = {
                "error_handler": self.error_handler is not None,
                "performance_monitor": self.performance_monitor is not None,
                "process_manager": self.process_manager is not None,
                "workflow": self.workflow is not None
            }

            # 检查配置
            config_valid = (
                self.config.max_processes > 0 and
                self.config.multi_tab_config.max_tabs > 0
            )

            all_healthy = all(components_status.values()) and config_valid

            return {
                "healthy": all_healthy,
                "components": components_status,
                "config_valid": config_valid,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def reset_system(self):
        """重置系统状态"""
        logger.info("重置多进程协调器系统状态")

        # 重置各个组件
        self.error_handler.clear_errors()
        self.performance_monitor.reset_stats()

        # 重新初始化进程管理器
        self.process_manager = ProcessManager(self.config)

        logger.info("系统状态重置完成")

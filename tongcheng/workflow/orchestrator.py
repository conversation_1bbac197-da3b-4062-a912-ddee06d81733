from typing import Dict, Any
from ..tools.logger import get_workflow_logger
from .scheduler import Scheduler
from .workflow import WorkflowCoordinator
from .error_handler import <PERSON>rrorHandler
from .state_manager import StateManager
from .performance_monitor import PerformanceMonitor
from .resource_manager import ResourceManager

# 使用统一日志管理器
logger = get_workflow_logger()

class Orchestrator:
    """
    Orchestrator 负责系统的整体调度与流程控制，集成六大管理模块。
    """
    def __init__(self):
        # 初始化六大管理模块
        self.scheduler = Scheduler()
        self.error_handler = ErrorHandler()
        self.state_manager = StateManager()
        self.performance_monitor = PerformanceMonitor()
        self.resource_manager = ResourceManager()

        # 初始化工作流协调器，注入依赖的模块
        self.workflow = WorkflowCoordinator(
            error_handler=self.error_handler,
            performance_monitor=self.performance_monitor
        )

    def main_workflow(self) -> Dict[str, Any]:
        """主工作流程 - 简化版，使用模块化组件"""
        logger.info("=== Orchestrator 开始执行主工作流程 ===")
        
        try:
            # 开始性能监控
            self.performance_monitor.start_monitoring()
            
            # 执行主工作流程
            result = self.workflow.execute_main_workflow()
            
            # 停止性能监控
            self.performance_monitor.stop_monitoring()
            
            # 生成执行报告
            report = self._generate_final_report()
            
            logger.info("=== Orchestrator 主工作流程执行完成 ===")
            return {
                "workflow_result": result,
                "execution_report": report
            }
            
        except Exception as e:
            logger.error(f"Orchestrator 主工作流程执行异常: {str(e)}")
            self.error_handler.record_error("Orchestrator工作流程异常", {"错误原因": str(e)})
            
            # 即使出错也要停止监控和生成报告
            self.performance_monitor.stop_monitoring()
            report = self._generate_final_report()
            
            raise e
            
    def schedule_and_execute_tasks(self, tasks: list) -> list:
        """调度并执行任务列表"""
        logger.info("=== 开始调度和执行任务 ===")
        
        # 调度任务
        self.scheduler.schedule_tasks(tasks)
        
        # 执行已调度的任务
        results = self.scheduler.execute_scheduled_tasks()
        
        # 清空已完成的任务
        self.scheduler.clear_tasks()
        
        return results
        
    def execute_single_workflow(self) -> Dict[str, Any]:
        """执行单次完整工作流程（包含所有步骤）"""
        return self.main_workflow()
        
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "scheduler_status": {
                "is_running": self.scheduler.is_running(),
                "pending_tasks": len(self.scheduler.get_task_status())
            },
            "performance_stats": self.performance_monitor.get_stats(),
            "error_count": len(self.error_handler.get_errors()),
            "recent_errors": self.error_handler.get_errors()[-5:] if self.error_handler.get_errors() else []
        }
        
    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终执行报告"""
        errors = self.error_handler.get_errors()
        report = self.performance_monitor.generate_report(errors)
        
        # 添加额外的系统信息
        report["system_info"] = {
            "调度器状态": "运行中" if self.scheduler.is_running() else "空闲",
            "待处理任务数": len(self.scheduler.get_task_status()),
            "总错误数": len(errors)
        }
        
        return report
        
    def reset_system(self):
        """重置系统状态"""
        logger.info("重置系统状态")
        self.scheduler.clear_tasks()
        self.error_handler.clear_errors()
        self.performance_monitor.reset_stats()
        
    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        return {
            "status": "healthy",
            "components": {
                "scheduler": "ok",
                "workflow": "ok", 
                "error_handler": "ok",
                "performance_monitor": "ok",
                "state_manager": "ok",
                "resource_manager": "ok"
            },
            "timestamp": self.performance_monitor.get_stats().get("start_time", "unknown")
        }


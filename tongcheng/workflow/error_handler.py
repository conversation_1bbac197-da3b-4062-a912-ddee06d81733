import time
from datetime import datetime
from typing import Dict, Any, List
from ..tools.logger import LoggerManager

# 使用统一日志管理器
logger = LoggerManager.get_logger("ErrorHandler")

class ErrorHandler:
    """
    错误处理模块，负责处理异常、记录错误和发送监控告警。
    """
    def __init__(self):
        self.errors = []
        
    def handle(self, error):
        """
        处理错误，error 为异常对象。
        """
        logger.error(f"处理异常: {str(error)}")
        
    def record_error(self, error_type: str, details: Dict[str, Any]):
        """记录错误信息"""
        error_record = {
            "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "错误类型": error_type,
            **details
        }
        self.errors.append(error_record)
        logger.error(f"记录错误: {error_record}")
        
    def get_errors(self) -> List[Dict[str, Any]]:
        """获取所有错误记录"""
        return self.errors
        
    def clear_errors(self):
        """清空错误记录"""
        self.errors.clear()
        
    def send_monitor_alert(self, title: str, message: str):
        """发送监控告警（待实现具体监控系统）"""
        logger.error(f"监控告警 - {title}: {message}")
        # TODO: 接入具体的监控系统，如邮件、短信、钉钉等
        
    def retry_with_config(self, func, max_retries: int, retry_interval: int, *args, **kwargs):
        """带重试机制的函数执行器"""
        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries:
                    logger.error(f"函数 {func.__name__} 执行失败，已达到最大重试次数 {max_retries}: {str(e)}")
                    # 发送监控告警
                    self.send_monitor_alert(f"函数 {func.__name__} 执行失败", str(e))
                    raise e
                else:
                    logger.warning(f"函数 {func.__name__} 执行失败，第 {attempt + 1} 次重试: {str(e)}")
                    time.sleep(retry_interval) 
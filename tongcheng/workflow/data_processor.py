from datetime import datetime
from typing import List, Dict, Any
from ..tools.logger import get_processor_logger
from ..models.hotel_models import Hotel, RoomType
from ..models.mapping_models import RoomMapping
from ..models.price_models import PriceCalendar
from ..processor.badazhou_processor import BadazhouProcessor

# 使用统一日志管理器
logger = get_processor_logger()

class DataProcessor:
    """
    数据处理器，负责数据匹配、转换等逻辑处理。
    """
    def __init__(self, error_handler=None, performance_monitor=None):
        self.error_handler = error_handler
        self.performance_monitor = performance_monitor
        # 初始化八大洲数据处理器
        self.badazhou_processor = BadazhouProcessor()

    def get_date_range(self, config: Dict[str, Any]) -> tuple:
        """获取查询的日期范围"""
        today = datetime.now().date()
        start_date = today.strftime("%Y-%m-%d")
        days = config["date_config"]["query_days"]
        return start_date, days

    def process_badazhou_data(self, hotel_price_data: List[Dict], date_list: List[str]) -> List[Dict]:
        """
        hotel_price_data: [{'checkInDate':..., 'rooms':[{'roomCode':...,'roomName':...,'averagePriceList':[{'2025-07-02': '2869.0'}, ...]}, ...]}, ...]
        date_list: ['2025-07-02', ..., '2025-07-31']
        返回: 每个房型有完整30天价格的结构
        """
        # 1. 聚合所有房型的价格
        room_price_map = {}  # (roomCode, roomName) -> {date: price}
        for daily in hotel_price_data:
            for room in daily.get("rooms", []):
                key = (room.get("roomCode"), room.get("roomName"))
                if key not in room_price_map:
                    room_price_map[key] = {}
                for price_dict in room.get("averagePriceList", []):
                    for date, price in price_dict.items():
                        room_price_map[key][date] = str(price)
        # 2. 补全所有日期
        result = []
        for (room_code, room_name), price_dict in room_price_map.items():
            full_price_dict = {}
            for date in date_list:
                full_price_dict[date] = price_dict.get(date, "9999")
            result.append({
                "roomCode": room_code,
                "roomName": room_name,
                "averagePriceList": [full_price_dict]
            })
        return result

    def filter_hotel_room_mappings(self, room_mappings: List[Dict], hotel_name: str) -> List[Dict]:
        """过滤特定酒店的房型映射关系"""
        hotel_room_mappings = [
            mapping for mapping in room_mappings if mapping.get("八大洲酒店名称", "") == hotel_name
        ]
        logger.info(f"酒店 {hotel_name} 有 {len(hotel_room_mappings)} 个房型映射关系")
        return hotel_room_mappings

    def extract_rooms_from_processed_data(self, processed_hotels: List[Dict]) -> List[Dict]:
        """从处理后的酒店数据中提取房型信息"""
        all_rooms = []
        for hotel_data in processed_hotels:
            if "rooms" in hotel_data:
                all_rooms.extend(hotel_data["rooms"])

        # 去重房型（基于房型名称和代码）
        unique_rooms = []
        seen_rooms = set()
        for room in all_rooms:
            room_key = (room.get("roomName", ""), room.get("roomCode", ""))
            if room_key not in seen_rooms:
                unique_rooms.append(room)
                seen_rooms.add(room_key)

        logger.info(f"提取到 {len(unique_rooms)} 个独特房型")
        return unique_rooms

    def match_rooms(self, hotel_rooms: List[Dict], room_mappings: List[Dict]) -> List[Dict]:
        """房型匹配逻辑，飞书的八大洲房型名称+床型与接口的roomName比对"""
        matched_rooms = []

        # 第一步：八大洲房型匹配同程房型（保持原有逻辑，但不记录错误）
        for room in hotel_rooms:
            room_name = room.get("roomName", "")
            room_code = room.get("roomCode", "")
            tongcheng_room = None
            mapping_full_name = None

            # 优先用组合字段比对（八大洲房型名称+床型）
            for mapping in room_mappings:
                mapping_bed_type = mapping.get("床型", "")
                mapping_badazhou_room_name = mapping.get("八大洲房型名称", "")
                # 构建飞书八大洲房型+床型
                mapping_full_name = f"{mapping_badazhou_room_name}({mapping_bed_type})" if mapping_bed_type else mapping_badazhou_room_name
                if mapping_full_name == room_name:
                    tongcheng_room = mapping.get("同程房型名称", "")
                    break

            # 若未匹配到，再用原有房型名比对
            if not tongcheng_room:
                for mapping in room_mappings:
                    if mapping.get("八大洲房型名称", "") == room_name:
                        tongcheng_room = mapping.get("同程房型名称", "")
                        mapping_full_name = room_name
                        break

            if tongcheng_room:
                matched_rooms.append({
                    "八大洲房型名称": room_name,
                    "八大洲房型编码": room_code,
                    "同程房型名称": tongcheng_room,
                    "价格数据": room.get("averagePriceList", [])
                })
                if self.performance_monitor:
                    self.performance_monitor.increment_counter("successful_matches")
                logger.info(f"房型匹配成功: {room_name} <- {mapping_full_name} -> {tongcheng_room}")
            else:
                # 八大洲有房型但同程没有对应的情况，只记录信息，不记录为错误
                if self.performance_monitor:
                    self.performance_monitor.increment_counter("badazhou_only_rooms")
                logger.info(f"八大洲房型无对应同程房型: {room_name} (房型编码: {room_code})")

        # 第二步：验证同程房型是否都有对应的八大洲房型（新增逻辑）
        self._validate_tongcheng_rooms(hotel_rooms, room_mappings)

        return matched_rooms

    def validate_hotel_data(self, hotel: Dict[str, Any]) -> bool:
        """验证酒店数据完整性"""
        hotel_code = hotel.get("八大洲酒店编码", "")
        hotel_name = hotel.get("八大洲酒店名称", "")
        tongcheng_hotel_name = hotel.get("同程酒店名称", "")

        if not hotel_code or not tongcheng_hotel_name:
            if self.error_handler:
                self.error_handler.record_error("酒店数据不完整", {
                    "酒店编码": hotel_code,
                    "八大洲酒店名称": hotel_name,
                    "同程酒店名称": tongcheng_hotel_name,
                    "错误原因": "缺少必要字段"
                })
            return False
        return True

    def format_push_data(self, hotel_name: str, matched_rooms: List[Dict]) -> Dict[str, Any]:
        """格式化推送数据"""
        return {
            "同程酒店名称": hotel_name,
            "房型数据": matched_rooms
        }
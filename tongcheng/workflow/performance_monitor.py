import logging
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """
    性能监控模块，负责记录和分析性能指标，生成执行报告。
    """
    def __init__(self):
        self.stats = {
            "start_time": None,
            "end_time": None,
            "processed_hotels": 0,
            "successful_matches": 0,
            "failed_matches": 0,
            "successful_pushes": 0,
            "failed_pushes": 0
        }
        
    def record(self, metric, value):
        """
        记录性能指标。
        """
        if metric in self.stats:
            self.stats[metric] = value
            logger.debug(f"记录指标: {metric} = {value}")
        else:
            logger.warning(f"未知指标: {metric}")
            
    def start_monitoring(self):
        """开始监控"""
        self.stats["start_time"] = datetime.now()
        logger.info("开始性能监控")
        
    def stop_monitoring(self):
        """停止监控"""
        self.stats["end_time"] = datetime.now()
        logger.info("停止性能监控")
        
    def increment_counter(self, counter_name: str, value: int = 1):
        """增加计数器值"""
        if counter_name in self.stats:
            self.stats[counter_name] += value
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计数据"""
        return self.stats.copy()
        
    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            "start_time": None,
            "end_time": None,
            "processed_hotels": 0,
            "successful_matches": 0,
            "failed_matches": 0,
            "successful_pushes": 0,
            "failed_pushes": 0
        }
        
    def generate_report(self, errors: list) -> Dict[str, Any]:
        """生成执行报告"""
        if not self.stats["start_time"] or not self.stats["end_time"]:
            logger.warning("监控未正确启动或停止，无法生成完整报告")
            return {}
            
        duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
        
        report = {
            "执行时间": {
                "开始时间": self.stats["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                "结束时间": self.stats["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                "执行时长": f"{duration:.2f}秒"
            },
            "处理统计": {
                "处理酒店数量": self.stats["processed_hotels"],
                "成功匹配房型数量": self.stats["successful_matches"],
                "失败匹配房型数量": self.stats["failed_matches"],
                "推送成功数量": self.stats["successful_pushes"],
                "推送失败数量": self.stats["failed_pushes"]
            },
            "错误详情": errors
        }
        
        logger.info("=== 执行报告 ===")
        logger.info(f"执行报告: {report}")
        
        return report 
"""
进程管理器 - 管理多进程的创建、监控和清理
"""

import time
import multiprocessing as mp
import threading
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from queue import Queue, Empty
import logging
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None
import os
from datetime import datetime

from ..tools.logger import get_workflow_logger
from ..config.multi_process_config import MultiProcessConfig

logger = get_workflow_logger()


@dataclass
class HotelTask:
    """酒店处理任务"""
    hotel_id: str
    hotel_name: str
    hotel_data: dict
    room_mappings: list
    task_id: str
    priority: int = 0


@dataclass
class ProcessResult:
    """进程处理结果"""
    task_id: str
    hotel_id: str
    hotel_name: str
    success: bool
    message: str
    processing_time: float
    room_count: int
    error_details: Optional[dict] = None


class ProcessMonitor:
    """进程监控器"""

    def __init__(self, config: MultiProcessConfig):
        self.config = config
        self.process_stats = {}
        self.monitoring_active = False
        self.monitor_thread = None

    def start_monitoring(self, process_ids: List[int]):
        """开始监控进程"""
        self.process_stats = {pid: [] for pid in process_ids}
        self.monitoring_active = True

        if self.config.log_process_metrics and PSUTIL_AVAILABLE:
            self.monitor_thread = threading.Thread(
                target=self._monitor_processes,
                args=(process_ids,),
                daemon=True
            )
            self.monitor_thread.start()
            logger.info(f"开始监控 {len(process_ids)} 个进程")
        elif self.config.log_process_metrics and not PSUTIL_AVAILABLE:
            logger.warning("psutil 不可用，跳过进程监控")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("进程监控已停止")

    def _monitor_processes(self, process_ids: List[int]):
        """监控进程资源使用"""
        if not PSUTIL_AVAILABLE:
            logger.warning("进程监控需要 psutil 库，跳过监控")
            return

        while self.monitoring_active:
            try:
                for pid in process_ids:
                    try:
                        process = psutil.Process(pid)
                        if process.is_running():
                            cpu_percent = process.cpu_percent()
                            memory_mb = process.memory_info().rss / 1024 / 1024

                            self.process_stats[pid].append({
                                'timestamp': datetime.now(),
                                'cpu_percent': cpu_percent,
                                'memory_mb': memory_mb
                            })

                            # 检查资源限制
                            if self.config.memory_limit_mb and memory_mb > self.config.memory_limit_mb:
                                logger.warning(f"进程 {pid} 内存使用超限: {memory_mb:.1f}MB > {self.config.memory_limit_mb}MB")

                            if self.config.cpu_limit_percent and cpu_percent > self.config.cpu_limit_percent:
                                logger.warning(f"进程 {pid} CPU使用超限: {cpu_percent:.1f}% > {self.config.cpu_limit_percent}%")

                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        # 进程已结束或无权限访问
                        continue

                time.sleep(5.0)  # 每5秒监控一次

            except Exception as e:
                logger.error(f"进程监控异常: {str(e)}")
                time.sleep(10.0)

    def get_stats_summary(self) -> Dict[str, Any]:
        """获取监控统计摘要"""
        summary = {}
        for pid, stats in self.process_stats.items():
            if stats:
                cpu_values = [s['cpu_percent'] for s in stats]
                memory_values = [s['memory_mb'] for s in stats]

                summary[pid] = {
                    'avg_cpu_percent': sum(cpu_values) / len(cpu_values),
                    'max_cpu_percent': max(cpu_values),
                    'avg_memory_mb': sum(memory_values) / len(memory_values),
                    'max_memory_mb': max(memory_values),
                    'sample_count': len(stats)
                }
        return summary


class ProcessManager:
    """进程管理器"""

    def __init__(self, config: MultiProcessConfig):
        self.config = config
        self.monitor = ProcessMonitor(config)
        self.active_processes = {}
        self.results = []

    def process_hotels_parallel(self, hotel_tasks: List[HotelTask]) -> List[ProcessResult]:
        """并行处理酒店任务"""
        if not self.config.enable_multi_process or self.config.max_processes <= 1:
            logger.info("使用单进程模式处理酒店")
            return self._process_hotels_sequential(hotel_tasks)

        total_parallel = self.config.max_processes * self.config.multi_tab_config.max_tabs
        logger.info(f"使用多进程多标签页模式处理 {len(hotel_tasks)} 个酒店")
        logger.info(f"配置: {self.config.max_processes}进程 × {self.config.multi_tab_config.max_tabs}标签页 = {total_parallel}x并行度")

        results = []

        try:
            # 使用进程池执行器
            with ProcessPoolExecutor(
                max_workers=self.config.max_processes,
                mp_context=mp.get_context('spawn')  # 使用spawn方式创建进程
            ) as executor:

                # 提交任务
                future_to_task = {}
                for task in hotel_tasks:
                    future = executor.submit(
                        self._process_single_hotel_worker,
                        task,
                        self.config
                    )
                    future_to_task[future] = task

                    # 进程启动间隔
                    if self.config.process_start_delay > 0:
                        time.sleep(self.config.process_start_delay)

                # 开始监控（如果可能的话）
                if self.config.log_process_metrics:
                    try:
                        # 获取工作进程PID（这在ProcessPoolExecutor中比较困难）
                        # 这里我们简化处理，只记录主要信息
                        logger.info("多进程监控已启用")
                    except Exception as e:
                        logger.warning(f"无法启动进程监控: {str(e)}")

                # 收集结果
                for future in as_completed(future_to_task, timeout=self.config.hotel_processing_timeout):
                    task = future_to_task[future]
                    try:
                        result = future.result()
                        results.append(result)

                        status = "✅ 成功" if result.success else "❌ 失败"
                        logger.info(f"酒店 {result.hotel_name} 处理完成: {status}, 耗时: {result.processing_time:.2f}秒")

                    except Exception as e:
                        error_result = ProcessResult(
                            task_id=task.task_id,
                            hotel_id=task.hotel_id,
                            hotel_name=task.hotel_name,
                            success=False,
                            message=f"进程执行异常: {str(e)}",
                            processing_time=0.0,
                            room_count=0,
                            error_details={"exception": str(e)}
                        )
                        results.append(error_result)
                        logger.error(f"酒店 {task.hotel_name} 处理异常: {str(e)}")

        except Exception as e:
            logger.error(f"多进程处理异常: {str(e)}")
            # 回退到单进程模式
            logger.info("回退到单进程模式")
            return self._process_hotels_sequential(hotel_tasks)

        finally:
            self.monitor.stop_monitoring()

        return results

    def _process_hotels_sequential(self, hotel_tasks: List[HotelTask]) -> List[ProcessResult]:
        """顺序处理酒店任务（单进程模式）"""
        results = []

        for task in hotel_tasks:
            try:
                result = self._process_single_hotel_worker(task, self.config)
                results.append(result)

                status = "✅ 成功" if result.success else "❌ 失败"
                logger.info(f"酒店 {result.hotel_name} 处理完成: {status}, 耗时: {result.processing_time:.2f}秒")

                # 酒店间处理间隔
                if self.config.hotel_interval > 0:
                    time.sleep(self.config.hotel_interval)

            except Exception as e:
                error_result = ProcessResult(
                    task_id=task.task_id,
                    hotel_id=task.hotel_id,
                    hotel_name=task.hotel_name,
                    success=False,
                    message=f"处理异常: {str(e)}",
                    processing_time=0.0,
                    room_count=0,
                    error_details={"exception": str(e)}
                )
                results.append(error_result)
                logger.error(f"酒店 {task.hotel_name} 处理异常: {str(e)}")

        return results

    @staticmethod
    def _process_single_hotel_worker(task: HotelTask, config: MultiProcessConfig) -> ProcessResult:
        """
        单个酒店处理工作函数（在子进程中执行）
        使用多标签页并行处理酒店的所有房型

        注意：这个函数会在子进程中执行，需要重新初始化所有依赖
        """
        start_time = time.time()
        process_id = os.getpid()

        try:
            # 在子进程中重新导入和初始化
            import sys
            import os

            # 确保路径正确
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            from tongcheng.workflow.workflow import WorkflowCoordinator
            from tongcheng.workflow.error_handler import ErrorHandler
            from tongcheng.workflow.performance_monitor import PerformanceMonitor
            from tongcheng.pusher.tongcheng.multi_tab_processor import MultiTabTongchengPusher
            from tongcheng.tools.logger import get_workflow_logger

            # 创建进程级别的日志器
            logger = get_workflow_logger()
            logger.info(f"进程 {process_id} 开始处理酒店: {task.hotel_name}")

            # 创建工作流协调器
            error_handler = ErrorHandler()
            performance_monitor = PerformanceMonitor()

            # 创建多标签页推送器
            logger.info(f"进程 {process_id} 初始化多标签页推送器: {config.multi_tab_config.max_tabs}个标签页")
            pusher = MultiTabTongchengPusher(
                cookies_path=config.multi_tab_config.cookies_path,
                headless=config.multi_tab_config.headless,
                max_tabs=config.multi_tab_config.max_tabs
            )

            # 连接推送器
            if not pusher.connect():
                logger.error(f"进程 {process_id} 推送器连接失败")
                return ProcessResult(
                    task_id=task.task_id,
                    hotel_id=task.hotel_id,
                    hotel_name=task.hotel_name,
                    success=False,
                    message=f"进程 {process_id} 推送器连接失败",
                    processing_time=time.time() - start_time,
                    room_count=0
                )

            logger.info(f"进程 {process_id} 推送器连接成功")

            try:
                # 处理酒店数据
                room_data = task.hotel_data.get("房型数据", [])
                logger.info(f"进程 {process_id} 开始处理酒店 {task.hotel_name} 的 {len(room_data)} 个房型")

                # 构建推送数据
                push_data = {
                    "同程酒店名称": task.hotel_name,
                    "房型数据": room_data
                }

                # 执行多标签页推送
                success = pusher.push_price(push_data)

                processing_time = time.time() - start_time

                if success:
                    logger.info(f"进程 {process_id} 成功处理酒店 {task.hotel_name}, 耗时: {processing_time:.2f}秒")
                    message = f"进程 {process_id} 多标签页处理成功"
                else:
                    logger.warning(f"进程 {process_id} 处理酒店 {task.hotel_name} 失败")
                    message = f"进程 {process_id} 多标签页处理失败"

                return ProcessResult(
                    task_id=task.task_id,
                    hotel_id=task.hotel_id,
                    hotel_name=task.hotel_name,
                    success=success,
                    message=message,
                    processing_time=processing_time,
                    room_count=len(room_data)
                )

            finally:
                # 确保断开连接
                logger.info(f"进程 {process_id} 清理资源")
                pusher.disconnect()

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"进程 {process_id} 处理异常: {str(e)}"

            # 尝试记录错误日志
            try:
                from tongcheng.tools.logger import get_workflow_logger
                logger = get_workflow_logger()
                logger.error(error_msg)
            except:
                print(error_msg)  # 如果日志器不可用，直接打印

            return ProcessResult(
                task_id=task.task_id,
                hotel_id=task.hotel_id,
                hotel_name=task.hotel_name,
                success=False,
                message=error_msg,
                processing_time=processing_time,
                room_count=0,
                error_details={"exception": str(e), "process_id": process_id}
            )

    def get_processing_summary(self, results: List[ProcessResult]) -> Dict[str, Any]:
        """获取处理摘要"""
        if not results:
            return {"total": 0, "success": 0, "failed": 0, "success_rate": 0.0}

        total = len(results)
        success_count = sum(1 for r in results if r.success)
        failed_count = total - success_count
        success_rate = success_count / total * 100

        total_time = sum(r.processing_time for r in results)
        avg_time = total_time / total if total > 0 else 0

        total_rooms = sum(r.room_count for r in results)

        return {
            "total_hotels": total,
            "success_hotels": success_count,
            "failed_hotels": failed_count,
            "success_rate": success_rate,
            "total_processing_time": total_time,
            "average_processing_time": avg_time,
            "total_rooms_processed": total_rooms,
            "monitor_stats": self.monitor.get_stats_summary()
        }

"""
多标签页多进程快速启动脚本
提供简化的启动选项，快速开始使用
"""

import sys
import os
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tongcheng.workflow.multi_process_orchestrator import MultiProcessOrchestrator
from tongcheng.config.multi_process_config import (
    MultiProcessConfig,
    DEFAULT_MULTI_PROCESS_CONFIG,
    SAFE_MULTI_PROCESS_CONFIG,
    FAST_MULTI_PROCESS_CONFIG
)
from tongcheng.config.multi_tab_config import MultiTabConfig
from tongcheng.tools.logger import get_workflow_logger

logger = get_workflow_logger()


def print_banner():
    """打印横幅"""
    print("🚀" * 40)
    print("同程酒店价格维护系统")
    print("多标签页多进程快速启动")
    print("🚀" * 40)
    print()


def show_quick_options():
    """显示快速选项"""
    print("⚡ 快速启动选项:")
    print()
    print("1. 🛡️ 安全模式 (推荐新手)")
    print("   • 2进程 × 2标签页 = 4倍加速")
    print("   • 稳定可靠，适合初次使用")
    print()
    print("2. ⚡ 标准模式 (推荐日常)")
    print("   • 2进程 × 3标签页 = 6倍加速")
    print("   • 效率与稳定性平衡")
    print()
    print("3. 🚀 高速模式 (推荐高配)")
    print("   • 3进程 × 5标签页 = 15倍加速")
    print("   • 最高效率，需要高性能机器")
    print()
    print("4. 🔧 自定义模式")
    print("   • 自定义进程和标签页数量")
    print()


def get_user_choice() -> int:
    """获取用户选择"""
    while True:
        try:
            choice = input("请选择模式 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return int(choice)
            else:
                print("❌ 请输入 1-4 之间的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            sys.exit(0)
        except Exception:
            print("❌ 输入无效，请重新输入")


def create_custom_config() -> MultiProcessConfig:
    """创建自定义配置（简化版）"""
    print("\n🔧 自定义配置:")
    
    try:
        # 进程数
        while True:
            try:
                processes = int(input("进程数量 (1-4): "))
                if 1 <= processes <= 4:
                    break
                else:
                    print("请输入 1-4 之间的数字")
            except ValueError:
                print("请输入有效数字")
        
        # 标签页数
        while True:
            try:
                tabs = int(input("每进程标签页数 (1-6): "))
                if 1 <= tabs <= 6:
                    break
                else:
                    print("请输入 1-6 之间的数字")
            except ValueError:
                print("请输入有效数字")
        
        # 无头模式
        headless = input("无头模式? (y/N): ").strip().lower() == 'y'
        
        # 创建配置
        tab_config = MultiTabConfig(
            max_tabs=tabs,
            enable_multi_tab=tabs > 1,
            headless=headless
        )
        
        config = MultiProcessConfig(
            max_processes=processes,
            enable_multi_process=processes > 1,
            multi_tab_config=tab_config
        )
        
        parallel = processes * tabs
        print(f"\n✅ 配置完成: {processes}进程 × {tabs}标签页 = {parallel}倍并行度")
        
        return config
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(0)


def get_config_by_choice(choice: int) -> MultiProcessConfig:
    """根据选择获取配置"""
    if choice == 1:
        print("✅ 已选择安全模式")
        return SAFE_MULTI_PROCESS_CONFIG
    elif choice == 2:
        print("✅ 已选择标准模式")
        return DEFAULT_MULTI_PROCESS_CONFIG
    elif choice == 3:
        print("✅ 已选择高速模式")
        return FAST_MULTI_PROCESS_CONFIG
    elif choice == 4:
        return create_custom_config()
    else:
        raise ValueError(f"无效选择: {choice}")


def check_prerequisites() -> bool:
    """检查前置条件"""
    print("🔍 检查系统要求...")
    
    # 检查Cookie文件
    cookie_files = ["cookies.json", "tongcheng_cookies.json"]
    cookie_found = False
    
    for cookie_file in cookie_files:
        if os.path.exists(cookie_file):
            cookie_found = True
            print(f"✅ 找到Cookie文件: {cookie_file}")
            break
    
    if not cookie_found:
        print("❌ 未找到Cookie文件")
        print("   请确保以下文件之一存在:")
        for cookie_file in cookie_files:
            print(f"   • {cookie_file}")
        return False
    
    print("✅ 系统要求检查通过")
    return True


def show_config_summary(config: MultiProcessConfig):
    """显示配置摘要"""
    parallel = config.max_processes * config.multi_tab_config.max_tabs
    
    print("\n📊 配置摘要:")
    print(f"   进程数: {config.max_processes}")
    print(f"   每进程标签页: {config.multi_tab_config.max_tabs}")
    print(f"   总并行度: {parallel}x")
    print(f"   无头模式: {'是' if config.multi_tab_config.headless else '否'}")
    print(f"   预估加速: {parallel}倍")


def run_workflow(config: MultiProcessConfig) -> bool:
    """运行工作流程"""
    print("\n🚀 开始执行...")
    print("=" * 50)
    
    try:
        # 创建协调器
        orchestrator = MultiProcessOrchestrator(config)
        
        # 执行工作流程
        result = orchestrator.main_workflow()
        
        # 显示结果
        if result.get("status") == "success":
            summary = result.get("summary", {})
            print("\n🎉 执行成功!")
            print(f"   处理酒店: {summary.get('total_hotels', 0)}")
            print(f"   成功酒店: {summary.get('success_hotels', 0)}")
            print(f"   成功率: {summary.get('success_rate', 0):.1f}%")
            return True
        else:
            print(f"\n❌ 执行失败: {result.get('message', '未知错误')}")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行异常: {str(e)}")
        logger.error(f"执行异常: {str(e)}")
        return False


def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 检查前置条件
        if not check_prerequisites():
            print("\n❌ 系统要求检查失败，请解决问题后重试")
            return 1
        
        # 显示选项
        show_quick_options()
        
        # 获取用户选择
        choice = get_user_choice()
        
        # 获取配置
        config = get_config_by_choice(choice)
        
        # 显示配置摘要
        show_config_summary(config)
        
        # 确认执行
        print("\n" + "⚡" * 30)
        confirm = input("确认开始执行? (Y/n): ").strip().lower()
        if confirm not in ['', 'y', 'yes']:
            print("👋 操作已取消")
            return 0
        
        # 运行工作流程
        success = run_workflow(config)
        
        if success:
            print("\n🎉 程序执行完成!")
            return 0
        else:
            print("\n❌ 程序执行失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return 0
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        logger.error(f"程序异常: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    print("\n感谢使用同程酒店价格维护系统!")
    sys.exit(exit_code)

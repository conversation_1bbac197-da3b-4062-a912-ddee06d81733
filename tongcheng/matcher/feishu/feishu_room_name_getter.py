from typing import Optional, List, Dict
import json
from tongcheng.matcher.feishu.table_base import TableBase

def parse_rich_text(val):
    """
    递归解析飞书富文本格式，支持字符串、dict、list、嵌套JSON等，最终返回纯文本
    """
    # 先处理字符串类型
    if isinstance(val, str):
        s = val.strip()
        # 如果是JSON字符串，递归解析
        if (s.startswith('{') and s.endswith('}')) or (s.startswith('[') and s.endswith(']')):
            try:
                obj = json.loads(s)
                return parse_rich_text(obj)
            except Exception:
                return s
        return s
    # 处理dict类型
    elif isinstance(val, dict):
        if 'text' in val:
            return val['text']
        if 'value' in val and isinstance(val['value'], list):
            return ''.join([parse_rich_text(item) for item in val['value']])
        return ''.join([parse_rich_text(v) for v in val.values()])
    # 处理list类型
    elif isinstance(val, list):
        return ''.join([parse_rich_text(item) for item in val])
    return str(val) if val is not None else ''

# 新增：安全strip函数，保证字段类型安全
def safe_strip(val):
    return val.strip() if isinstance(val, str) else (str(val).strip() if val is not None else "")

class RoomNameGetter(TableBase):
    def __init__(self, app_token: str, table_id: str, view_id: str, user_access_token: Optional[str] = None, use_tenant_token: bool = False):
        super().__init__(app_token, table_id, view_id, user_access_token, use_tenant_token)

    def get_room_names(self) -> List[Dict[str, str]]:
        """
        提取表格中第2、3、4、5列（同程酒店名称、八大洲酒店名称、同程房型名称、八大洲房型名称）
        返回：[{"同程酒店名称":..., "八大洲酒店名称":..., "同程房型名称":..., "八大洲房型名称":...}, ...]
        """
        data = self.get_table_data()
        return self._extract_room_name(data)

    def _extract_room_name(self, data) -> List[Dict[str, str]]:
        result = []
        if not data or not hasattr(data, 'items'):
            return result
        for item in data.items:
            fields = item.fields if hasattr(item, 'fields') else item.get('fields', {})
            result.append({
                "同程酒店名称": parse_rich_text(fields.get("同程酒店名称", "")),
                "八大洲酒店名称": parse_rich_text(fields.get("八大洲酒店名称", "")),
                '床型': parse_rich_text(fields.get("床型", "")),
                "同程房型名称": parse_rich_text(fields.get("同程房型名称", "")),
                "八大洲房型名称": parse_rich_text(fields.get("八大洲房型名称", ""))
            })
        return result

if __name__ == '__main__':
    room_name = RoomNameGetter("QQs8bSTMUaMX4QsiRQScm3Xonxd", "tblTPWrdXsEo3glb", "vewCdS7r5w")
    print(room_name.get_room_names())

import json
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from typing import List, Dict, Optional

import time

from tongcheng.matcher.feishu.feishu_token_manager import get_tenant_access_token


# from matcher.feishu.feishu_token_manager import get_tenant_access_token


class TableBase:
    """飞书多维表格基类"""
    
    def __init__(self, app_token: str, table_id: str, view_id: str, user_access_token: Optional[str] = None, use_tenant_token: bool = False):
        """初始化基类
        
        Args:
            app_token: 应用token
            table_id: 表格ID
            view_id: 视图ID
            user_access_token: 用户访问token
            use_tenant_token: 是否使用tenant_access_token
        """
        self.app_token = app_token
        self.table_id = table_id
        self.view_id = view_id
        self.user_access_token = None if use_tenant_token else user_access_token
        self.use_tenant_token = use_tenant_token
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 1  # 重试延迟（秒）
        
        # 如果使用tenant_token或没有提供user_access_token，则获取tenant_access_token
        if use_tenant_token or not self.user_access_token:
            self.tenant_access_token = get_tenant_access_token()
            if not self.tenant_access_token:
                raise Exception("获取tenant_access_token失败")

    def _create_client(self):
        """创建飞书客户端"""
        client = lark.Client.builder() \
            .app_id("cli_a7401fd08929d01c") \
            .app_secret("UmbyI1rYDSZ5IaOXqNIgUhNVbzOPygbb") \
            .enable_set_token(True) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()
        return client

    def _build_request(self, page_token: str = None):
        """构建请求对象
        
        Args:
            page_token: 分页标记，用于获取下一页数据
        """
        builder = SearchAppTableRecordRequest.builder() \
            .app_token(self.app_token) \
            .table_id(self.table_id) \
            .user_id_type("user_id") \
            .page_size(100)
        
        if page_token:
            builder.page_token(page_token)
            
        builder.request_body(SearchAppTableRecordRequestBody.builder()
                        .view_id(self.view_id)
                        .build())
        
        return builder.build()

    def _send_request(self, client, request):
        """发送请求并获取响应"""
        if self.user_access_token:
            # 使用user_access_token
            option = lark.RequestOption.builder().user_access_token(self.user_access_token).build()
        else:
            # 使用tenant_access_token
            option = lark.RequestOption.builder().tenant_access_token(self.tenant_access_token).build()
        
        return client.bitable.v1.app_table_record.search(request, option)

    def _handle_response(self, response):
        """处理响应结果"""
        if not response.success():
            self._log_error(response)
            return None
        return response.data

    def _log_error(self, response):
        """记录错误日志"""
        lark.logger.error(
            f"请求失败, code: {response.code}, msg: {response.msg}, "
            f"log_id: {response.get_log_id()}, "
            f"resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")

    def _is_token_error(self, response):
        """检查是否是token相关的错误
        
        Args:
            response: 飞书API响应对象
            
        Returns:
            bool: 是否是token错误
        """
        if not response.success():
            error_code = response.code
            error_msg = response.msg.lower()
            
            # 检查错误码
            token_error_codes = [401, 403, 40101, 40102, 40103, 40104]
            if error_code in token_error_codes:
                return True
                
            # 检查错误信息
            token_error_keywords = [
                "token expired",
                "invalid token",
                "token invalid",
                "token not found",
                "token verification failed",
                "未授权",
                "token已过期",
                "token无效"
            ]
            return any(keyword in error_msg for keyword in token_error_keywords)
        return False

    def _refresh_token(self):
        """刷新token
        
        Returns:
            bool: 是否刷新成功
        """
        if self.use_tenant_token or not self.user_access_token:
            new_token = get_tenant_access_token()
            if new_token:
                self.tenant_access_token = new_token
                return True
        return False

    def get_table_data(self):
        """获取表格所有数据，失败时会尝试刷新token重试"""
        client = self._create_client()
        all_items = []
        page_token = None
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                return self._fetch_table_data(client, page_token)
            except Exception as e:
                retry_count += 1
                error_msg = str(e).lower()
                
                # 检查是否是token错误
                if self._is_token_error(e) if hasattr(e, 'code') else any(keyword in error_msg for keyword in ["token", "未授权", "401", "403"]):
                    print(f"检测到token错误，第{retry_count}次尝试刷新token...")
                    if self._refresh_token():
                        print("token刷新成功，准备重试...")
                        time.sleep(self.retry_delay)  # 等待一秒后重试
                        continue
                    else:
                        print("token刷新失败")
                        break
                else:
                    print(f"发生非token错误: {str(e)}")
                    break
                    
        print(f"获取表格数据失败，已重试{retry_count}次")
        return None

    def _fetch_table_data(self, client, page_token=None):
        """实际获取表格数据的逻辑"""
        all_items = []
        
        while True:
            request = self._build_request(page_token)
            response = self._send_request(client, request)
            data = self._handle_response(response)
            
            if not data or not data.items:
                break
                
            all_items.extend(data.items)
            
            # 检查是否还有更多数据
            if not hasattr(data, 'page_token') or not data.page_token:
                break
                
            page_token = data.page_token
            
        # 构造返回结果，保持与原有格式一致
        return type('TableData', (), {'items': all_items})() if all_items else None

from tongcheng.models.mapping_models import RoomMapping
from typing import List, Dict

class FeishuMatcher:
    """
    只负责将酒店表和房型表的数据进行归并和标准化输出，不涉及任何飞书API参数。
    """
    def __init__(self, hotel_data: List[Dict], room_data: List[Dict]):
        self.hotel_data = hotel_data
        self.room_data = room_data

    def get_standardized_hotel_mappings(self) -> List[Dict]:
        """
        获取标准化酒店映射关系列表，每个酒店包含八大洲酒店编号、同程酒店名称、八大洲酒店名称、房型映射列表
        """
        # 以(八大洲酒店名称, 同程酒店名称)为key建立酒店字典
        hotel_dict = {}
        for item in self.hotel_data:
            key = (item.get("八大洲酒店名称", ""), item.get("同程酒店名称", ""))
            hotel_dict[key] = {
                "badazhou_hotel_id": item.get("八大洲酒店编码", ""),
                "source_hotel_name": item.get("同程酒店名称", ""),
                "target_hotel_name": item.get("八大洲酒店名称", ""),
                "room_mappings": []
            }

        # 遍历房型，归属到对应酒店
        for r in self.room_data:
            key = (r.get("八大洲酒店名称", ""), r.get("同程酒店名称", ""))
            if key in hotel_dict:
                hotel_dict[key]["room_mappings"].append(RoomMapping(
                    tongcheng_hotel_name=r.get("同程酒店名称", ""),
                    badazhou_hotel_name=r.get("八大洲酒店名称", ""),
                    tongcheng_room_name=r.get("同程房型名称", ""),
                    badazhou_room_name=r.get("八大洲房型名称", "")
                ))

        # 输出标准化结构
        result = []
        for v in hotel_dict.values():
            result.append({
                "badazhou_hotel_id": v["badazhou_hotel_id"],
                "source_hotel_name": v["source_hotel_name"],
                "target_hotel_name": v["target_hotel_name"],
                "room_mappings": v["room_mappings"]
            })
        return result

# 使用示例
if __name__ == '__main__':
    # 假设已通过HotelNameGetter和RoomNameGetter获取到hotel_data和room_data
    hotel_data = []  # 这里填实际数据
    room_data = []   # 这里填实际数据
    matcher = FeishuMatcher(hotel_data, room_data)
    for hotel in matcher.get_standardized_hotel_mappings():
        print(hotel)


import json
from typing import Optional

import lark_oapi as lark
from lark_oapi.api.auth.v3 import *


class FeishuTokenManager:
    def __init__(self):
        # 创建client
        self.client = lark.Client.builder() \
            .app_id("cli_a7401fd08929d01c") \
            .app_secret("UmbyI1rYDSZ5IaOXqNIgUhNVbzOPygbb") \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

    def get_tenant_access_token(self) -> Optional[str]:
        """
        获取最新的tenant_access_token

        Returns:
            Optional[str]: 成功返回token字符串，失败返回None
        """
        try:
            # 构造请求对象
            request: InternalTenantAccessTokenRequest = InternalTenantAccessTokenRequest.builder() \
                .request_body(InternalTenantAccessTokenRequestBody.builder()
                              .app_id("cli_a7401fd08929d01c")
                              .app_secret("UmbyI1rYDSZ5IaOXqNIgUhNVbzOPygbb")
                              .build()) \
                .build()

            # 发起请求
            response: InternalTenantAccessTokenResponse = self.client.auth.v3.tenant_access_token.internal(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"获取token失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None

            # 直接从response中获取token
            if hasattr(response, 'tenant_access_token'):
                return response.tenant_access_token

            # 如果上面的方式不行，尝试从raw content中解析
            content = json.loads(response.raw.content)
            if 'tenant_access_token' in content:
                return content['tenant_access_token']

            lark.logger.error(f"无法从响应中获取token: {content}")
            return None

        except Exception as e:
            lark.logger.error(f"获取token时发生异常: {str(e)}")
            return None


# 创建全局的token管理器实例
token_manager = FeishuTokenManager()


def get_tenant_access_token() -> Optional[str]:
    """
    获取最新的tenant_access_token的便捷方法

    Returns:
        Optional[str]: 成功返回token字符串，失败返回None
    """
    return token_manager.get_tenant_access_token()


if __name__ == "__main__":
    # 测试获取token
    token = get_tenant_access_token()
    if token:
        print("获取token成功：", token)
    else:
        print("获取token失败")

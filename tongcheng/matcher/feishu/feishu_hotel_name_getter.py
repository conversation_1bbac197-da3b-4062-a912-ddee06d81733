from typing import Optional, List, Dict
import json
from tongcheng.matcher.feishu.table_base import TableBase

# 解析飞书富文本格式
def parse_rich_text(val):
    """
    解析飞书富文本格式，提取纯文本内容
    支持字符串、dict、list等多种格式
    """
    if isinstance(val, str):
        # 处理字符串富文本JSON格式
        if val.startswith('[') and val.endswith(']'):
            try:
                rich_text_list = json.loads(val)
                if isinstance(rich_text_list, list) and len(rich_text_list) > 0:
                    first_item = rich_text_list[0]
                    if isinstance(first_item, dict) and 'text' in first_item:
                        return first_item['text']
            except (json.J<PERSON>odeError, KeyError, IndexError):
                pass
        return val
    elif isinstance(val, dict):
        # 处理dict格式富文本
        if 'value' in val and isinstance(val['value'], list):
            texts = []
            for item in val['value']:
                if isinstance(item, dict) and 'text' in item:
                    texts.append(item['text'])
            return ''.join(texts)
        elif 'text' in val:
            return val['text']
    elif isinstance(val, list):
        # 处理list格式富文本
        texts = []
        for item in val:
            t = parse_rich_text(item)
            if t:
                texts.append(t)
        return ''.join(texts)
    return str(val) if val is not None else ''

# 类型安全strip函数
def safe_strip(val):
    # 先解析富文本格式
    parsed_val = parse_rich_text(val)
    return parsed_val.strip() if isinstance(parsed_val, str) else (str(parsed_val).strip() if parsed_val is not None else "")

class HotelNameGetter(TableBase):
    def __init__(self, app_token: str, table_id: str, view_id: str, user_access_token: Optional[str] = None, use_tenant_token: bool = False):
        super().__init__(app_token, table_id, view_id, user_access_token, use_tenant_token)

    def get_hotel_names(self) -> List[Dict[str, str]]:
        """
        提取表格中八大洲酒店编码、no、八大洲酒店名称、同程酒店名称四列
        返回：[{"八大洲酒店编码":..., "no":..., "八大洲酒店名称":..., "同程酒店名称":...}, ...]
        """
        data = self.get_table_data()
        return self._extract_hotel_name(data)

    def _extract_hotel_name(self, data) -> List[Dict[str, str]]:
        result = []
        if not data or not hasattr(data, 'items'):
            return result
        for item in data.items:
            fields = item.fields
            result.append({
                "八大洲酒店编码": parse_rich_text(safe_strip(fields.get("八大洲酒店编码", ""))),
                "八大洲酒店名称": parse_rich_text(safe_strip(fields.get("八大洲酒店名称", ""))),
                "同程酒店名称": parse_rich_text(safe_strip(fields.get("同程酒店名称", "")))
            })
        return result

if __name__ == '__main__':
    hotel_name = HotelNameGetter("QQs8bSTMUaMX4QsiRQScm3Xonxd", "tblhzLBDnVYb5TqT", "vewGWxxmS7")
    print(hotel_name.get_hotel_names())

from datetime import datetime
from typing import List, Optional
from .base_models import BaseModel, MappingStatus
from .hotel_models import Hotel, RoomType
from pydantic import BaseModel

class HotelMapping(BaseModel):
    """酒店映射关系"""
    source_hotel_name: str
    target_hotel_name: str
    room_mappings: List['RoomMapping'] = []


class RoomMapping(BaseModel):
    """
    房型映射关系，直接对应飞书表每一行
    """
    tongcheng_hotel_name: str  # 同程酒店名称
    badazhou_hotel_name: str   # 八大洲酒店名称
    tongcheng_room_name: str   # 同程房型名称
    badazhou_room_name: str    # 八大洲房型名称
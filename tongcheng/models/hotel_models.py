from typing import List, Dict
from datetime import date
from .base_models import BaseEntity, PlatformType
from .price_models import PriceCalendar


class Hotel:
    """酒店模型"""
    def __init__(self, id: str, name: str, room_types: List['RoomType']):
        self.id = id
        self.name = name
        self.room_types = room_types  # 关联的房型列表


class RoomType:
    """房型模型"""
    def __init__(self, id: str, name: str, average_price_list: List[PriceCalendar]):
        self.id = id  # 房型ID
        self.name = name  # 房型名称
        self.average_price_list = average_price_list  # 每日价格列表

    hotel_id: str  # 所属酒店ID
    daily_prices: Dict[date, float] = {}  # 日期到价格的映射
    # 计算属性：获取价格日历对象列表
    @property
    def price_calendars(self) -> List[PriceCalendar]:
        return [
            PriceCalendar(
                room_type_id=self.id,
                base_price=price
            )
            for date, price in self.daily_prices.items()
        ]
from enum import Enum
from datetime import date, datetime
from pydantic import BaseModel  # 推荐使用Pydantic进行数据验证


class PlatformType(str, Enum):
    SOURCE = "source"
    TARGET = "target"


class MappingStatus(str, Enum):
    AUTO_MATCHED = "auto"
    MANUAL_MATCHED = "manual"
    PENDING = "pending"
    CONFLICT = "conflict"


class BaseEntity(BaseModel):
    """所有实体的基类"""
    id: str
    name: str
    platform: PlatformType
    normalized_name: str = ""

    class Config:
        use_enum_values = True  # 序列化时使用枚举值
        arbitrary_types_allowed = True
# 导出所有模型，方便外部导入
from .base_models import PlatformType, MappingStatus
from .hotel_models import Hotel, RoomType
from .mapping_models import HotelMapping, RoomMapping
from .sync_models import SyncTask, SyncError
from .price_models import PriceCalendar
from .config_models import PlatformConfig

__all__ = [
    'PlatformType', 'MappingStatus',
    'Hotel', 'RoomType',
    'HotelMapping', 'RoomMapping',
    'SyncTask', 'SyncError',
    'PriceCalendar',
    'PlatformConfig'
]
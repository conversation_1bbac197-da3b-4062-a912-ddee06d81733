from datetime import datetime
from typing import List, Optional
from .base_models import BaseModel

class SyncError(BaseModel):
    """同步错误记录"""
    timestamp: datetime
    entity_type: str  # hotel/room/mapping
    entity_id: str
    error_code: str
    message: str
    retry_count: int = 0

class SyncTask(BaseModel):
    """同步任务记录"""
    task_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    hotels_processed: int = 0
    rooms_processed: int = 0
    prices_updated: int = 0
    error_count: int = 0
    errors: List[SyncError] = []
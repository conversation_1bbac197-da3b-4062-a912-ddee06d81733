#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志统计功能
"""

import sys
import os
import logging
from datetime import datetime

# 设置简单的日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class ClickStatistics:
    """
    点击统计类，用于跟踪各种点击次数
    """
    def __init__(self):
        self.total_clicks = 0  # 总点击次数
        self.hotel_clicks = {}  # 每个酒店的点击次数 {hotel_name: count}
        self.room_clicks = {}  # 每个房型的点击次数 {hotel_name: {room_name: count}}
        self.hotel_room_counts = {}  # 每个酒店的房型数量 {hotel_name: room_count}
        self.start_time = datetime.now()
        
    def add_room_click(self, hotel_name: str, room_name: str, click_count: int = 1):
        """添加房型点击次数"""
        # 更新总点击次数
        self.total_clicks += click_count
        
        # 更新酒店点击次数
        if hotel_name not in self.hotel_clicks:
            self.hotel_clicks[hotel_name] = 0
        self.hotel_clicks[hotel_name] += click_count
        
        # 更新房型点击次数
        if hotel_name not in self.room_clicks:
            self.room_clicks[hotel_name] = {}
        if room_name not in self.room_clicks[hotel_name]:
            self.room_clicks[hotel_name][room_name] = 0
        self.room_clicks[hotel_name][room_name] += click_count
        
    def add_hotel_room_count(self, hotel_name: str, room_count: int):
        """记录酒店的房型数量"""
        self.hotel_room_counts[hotel_name] = room_count
        
    def get_statistics_summary(self):
        """获取统计摘要"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        return {
            "执行时间": {
                "开始时间": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "结束时间": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "总耗时": str(duration)
            },
            "总点击次数": self.total_clicks,
            "处理酒店数量": len(self.hotel_clicks),
            "酒店点击统计": self.hotel_clicks,
            "房型点击统计": self.room_clicks,
            "酒店房型数量": self.hotel_room_counts
        }
        
    def print_statistics(self):
        """输出详细统计信息到日志"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info("\n" + "=" * 60)
        logger.info("点击统计报告")
        logger.info("=" * 60)
        
        # 基本统计信息
        logger.info(f"执行时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总耗时: {duration}")
        logger.info(f"总点击次数: {self.total_clicks}")
        logger.info(f"处理酒店数量: {len(self.hotel_clicks)}")
        
        # 计算平均值
        total_rooms = sum(self.hotel_room_counts.values())
        if len(self.hotel_clicks) > 0:
            avg_clicks_per_hotel = self.total_clicks / len(self.hotel_clicks)
            logger.info(f"平均每个酒店点击次数: {avg_clicks_per_hotel:.2f}")
            
        if total_rooms > 0:
            avg_clicks_per_room = self.total_clicks / total_rooms
            logger.info(f"平均每个房型点击次数: {avg_clicks_per_room:.2f}")
        
        logger.info("\n" + "-" * 60)
        logger.info("各酒店点击统计详情")
        logger.info("-" * 60)
        
        # 按点击次数排序显示酒店
        sorted_hotels = sorted(self.hotel_clicks.items(), key=lambda x: x[1], reverse=True)
        
        for i, (hotel_name, clicks) in enumerate(sorted_hotels, 1):
            room_count = self.hotel_room_counts.get(hotel_name, 0)
            logger.info(f"{i}. 酒店: {hotel_name}")
            logger.info(f"   总点击次数: {clicks}")
            logger.info(f"   房型数量: {room_count}")
            
            if room_count > 0:
                avg_clicks_per_room_hotel = clicks / room_count
                logger.info(f"   平均每房型点击: {avg_clicks_per_room_hotel:.2f}")
            
            if hotel_name in self.room_clicks:
                # 按点击次数排序显示房型
                sorted_rooms = sorted(self.room_clicks[hotel_name].items(), key=lambda x: x[1], reverse=True)
                logger.info(f"   房型点击详情:")
                for room_name, room_clicks in sorted_rooms:
                    logger.info(f"     - {room_name}: {room_clicks} 次")
            logger.info("")  # 空行分隔
        
        # 最高和最低点击统计
        if self.hotel_clicks:
            max_hotel = max(self.hotel_clicks.items(), key=lambda x: x[1])
            min_hotel = min(self.hotel_clicks.items(), key=lambda x: x[1])
            
            logger.info("-" * 60)
            logger.info("极值统计")
            logger.info("-" * 60)
            logger.info(f"点击次数最多的酒店: {max_hotel[0]} ({max_hotel[1]} 次)")
            logger.info(f"点击次数最少的酒店: {min_hotel[0]} ({min_hotel[1]} 次)")
            
            # 找出点击次数最多和最少的房型
            all_room_clicks = []
            for hotel_rooms in self.room_clicks.values():
                for room_name, clicks in hotel_rooms.items():
                    all_room_clicks.append((room_name, clicks))
            
            if all_room_clicks:
                max_room = max(all_room_clicks, key=lambda x: x[1])
                min_room = min(all_room_clicks, key=lambda x: x[1])
                logger.info(f"点击次数最多的房型: {max_room[0]} ({max_room[1]} 次)")
                logger.info(f"点击次数最少的房型: {min_room[0]} ({min_room[1]} 次)")
        
        logger.info("\n" + "=" * 60)
        logger.info("统计报告结束")
        logger.info("=" * 60)

def test_log_statistics():
    """测试日志统计功能"""
    
    print("=== 测试日志统计功能 ===")
    
    # 创建统计对象
    stats = ClickStatistics()
    
    # 模拟添加统计数据
    print("\n1. 添加测试数据...")
    
    # 酒店1：新加坡费尔蒙酒店
    hotel1 = "新加坡费尔蒙酒店"
    stats.add_hotel_room_count(hotel1, 3)
    stats.add_room_click(hotel1, "豪华大床房", 25)
    stats.add_room_click(hotel1, "豪华大床房（港景，带阳台）", 32)
    stats.add_room_click(hotel1, "特色套房", 18)
    
    # 酒店2：香港半岛酒店
    hotel2 = "香港半岛酒店"
    stats.add_hotel_room_count(hotel2, 2)
    stats.add_room_click(hotel2, "豪华客房", 28)
    stats.add_room_click(hotel2, "半岛套房", 45)
    
    # 酒店3：东京帝国酒店
    hotel3 = "东京帝国酒店"
    stats.add_hotel_room_count(hotel3, 4)
    stats.add_room_click(hotel3, "帝国客房", 22)
    stats.add_room_click(hotel3, "帝国套房", 38)
    stats.add_room_click(hotel3, "皇家套房", 55)
    stats.add_room_click(hotel3, "总统套房", 67)
    
    print("✓ 测试数据添加完成")
    
    # 2. 输出统计信息到日志
    print("\n2. 输出统计报告到日志...")
    stats.print_statistics()
    
    print("\n✓ 日志统计功能测试完成！")
    print("注意：统计信息已经完全输出到日志中，不再生成JSON文件。")
    
    return True

if __name__ == "__main__":
    success = test_log_statistics()
    
    if success:
        print("\n🎉 测试通过！日志统计功能实现成功。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！需要检查日志统计功能。")
        sys.exit(1)
